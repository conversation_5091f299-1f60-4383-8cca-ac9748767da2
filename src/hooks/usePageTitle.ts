/**
 * @description This custom React hook sets the document title for a component. It takes a title string as an argument and updates the document's title whenever the component mounts or the title prop changes. This is used across different pages to ensure each page has a unique, SEO-friendly title. The main variable is 'title', which is the string to be set as the page title.
 */
import { useEffect } from "react";

const usePageTitle = (title) => {
	useEffect(() => {
		document.title = title;
	}, [title]);
};

export default usePageTitle;

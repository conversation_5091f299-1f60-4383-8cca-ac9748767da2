/**
 * @description This component renders a comprehensive and SEO-optimized detail page for roofing services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for roofing.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	ArrowRight,
	Home,
	ShieldCheck,
	Umbrella,
} from "lucide-react";

const Service_DakbedekkingPage = () => {
	usePageTitle("Dakbedekking | Klusgebied - Voor een Waterdicht & Veilig Dak");
	const navigate = useNavigate();

	const services = [
		{
			title: "Bitumen Dakbedekking",
			description:
				"Duurzame en waterdichte dakbedekking voor platte en licht hellende daken.",
			longDescription:
				"Bitumen (ook wel dakleer genoemd) is een betrouwbare en kosteneffectieve keuze voor platte daken. Wij gebruiken hoogwaardige, gemodificeerde bitumen die jarenlang meegaan. De dakbedekking wordt met een brander aangebracht voor een naadloze, waterdichte afwerking.",
		},
		{
			title: "EPDM Dakbedekking",
			description:
				"Elastische, onderhoudsvriendelijke en zeer duurzame rubberen dakfolie.",
			longDescription:
				"EPDM is een synthetisch rubber dat uitermate geschikt is voor platte daken. Het is zeer elastisch, UV-bestendig en heeft een levensduur van wel 50 jaar. Wij leggen EPDM-folie uit één stuk, waardoor de kans op lekkages minimaal is. Ideaal voor daken met zonnepanelen of een groendak.",
		},
		{
			title: "Dakpannen Leggen & Vervangen",
			description:
				"Voor hellende daken, van het vervangen van enkele pannen tot een compleet nieuw pannendak.",
			longDescription:
				"Of u nu een paar kapotte dakpannen wilt vervangen of uw volledige pannendak wilt vernieuwen, onze dakdekkers staan voor u klaar. Wij werken met alle soorten dakpannen (keramisch en beton) en zorgen voor een perfecte plaatsing, inclusief panlatten, folie en nokvorsten.",
		},
		{
			title: "Dakinspectie & Onderhoud",
			description:
				"Periodieke controle en onderhoud om de levensduur van uw dak te verlengen.",
			longDescription:
				"Voorkom problemen met een jaarlijkse dakinspectie. Wij controleren uw dak op zwakke plekken, reinigen de dakgoten en voeren klein onderhoud uit. Zo verlengt u de levensduur van uw dak en voorkomt u onverwachte, kostbare reparaties.",
		},
	];

	const benefits = [
		{
			icon: <Umbrella className="w-8 h-8 text-white" />,
			title: "Gegarandeerd Waterdicht",
			description:
				"Professionele installatie voor een dak dat u beschermt tegen alle weersomstandigheden.",
		},
		{
			icon: <Home className="w-8 h-8 text-white" />,
			title: "Alle Soorten Daken",
			description:
				"Onze dakdekkers hebben ervaring met zowel platte als hellende daken.",
		},
		{
			icon: <ShieldCheck className="w-8 h-8 text-white" />,
			title: "Lange Levensduur",
			description:
				"Wij werken met hoogwaardige materialen die een lange levensduur garanderen.",
		},
	];

	const faqs = [
		{
			question: "Hoe lang gaat een dak mee?",
			answer:
				"De levensduur hangt af van het materiaal. Bitumen gaat gemiddeld 20-25 jaar mee, EPDM 40-50 jaar, en dakpannen kunnen wel 50 tot 100 jaar meegaan, afhankelijk van het type.",
		},
		{
			question: "Wat kost nieuwe dakbedekking?",
			answer:
				"Voor een plat dak met bitumen of EPDM liggen de kosten tussen de €60 en €100 per m². Een nieuw pannendak is duurder, reken op €100 tot €200 per m².",
		},
		{
			question: "Mijn dak lekt, wat nu?",
			answer:
				"Neem direct contact op om verdere waterschade te voorkomen. Een dakdekker kan een noodreparatie uitvoeren en daarna de definitieve oplossing adviseren.",
		},
		{
			question: "Is een groen dak (sedumdak) een goede optie?",
			answer:
				"Ja, een groen dak heeft veel voordelen. Het isoleert, verlengt de levensduur van uw dakbedekking, en is goed voor de biodiversiteit. Wij kunnen u hierover adviseren.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1582034536883-0d381176366b?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1726589004565-bedfba94d3a2?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxyb29maW5nJTIwd29yayUyQyUyMGNvbnN0cnVjdGlvbiUyMHNpdGUlMkMlMjByb29maW5nJTIwbWF0ZXJpYWxzfGVufDB8fHx8MTc1MTc0MjUxMHww&ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1741446458387-74132b7d49cc?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxyb29mJTIwaW5zdGFsbGF0aW9uJTJDJTIwcHJvZmVzc2lvbmFsJTIwcm9vZmVyJTJDJTIwcm9vZmluZyUyMHNlcnZpY2VzfGVufDB8fHx8MTc1MTc0MjUxMXww&ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{ type: "image", url: "https://heyboss.heeyo.ai/1751742512-1a5f7a9e.webp" },
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1582034536883-0d381176366b?ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Dakdekker aan het werk op een dak"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Nieuwe Dakbedekking Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Voor een duurzaam, waterdicht en veilig dak. Vakkundig gelegd
								door ervaren dakdekkers.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een dakdekker{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Dakdiensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Complete oplossingen voor elk type dak.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{services.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Zekerheid van een Goed Dak
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Kies voor vakmanschap en bescherm uw huis tegen de elementen.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-red-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				<section className="bg-gradient-to-r from-red-600 to-slate-800">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Is uw dak aan vervanging toe?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Wacht niet op lekkages. Vraag een vrijblijvende dakinspectie en
							offerte aan.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-red-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Vraag nu een offerte aan
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_DakbedekkingPage;

/**
 * @description This component renders a comprehensive and SEO-optimized detail page for upholstery services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for upholsterers.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import { ArrowLeft, ArrowRight, Sofa, Scissors, Award } from "lucide-react";

const Service_StoffeerderPage = () => {
	usePageTitle("Stoffeerder Nodig? | Klusgebied - Meubels & Trappen Stofferen");
	const navigate = useNavigate();

	const upholsteryServices = [
		{
			title: "Meubels Herstofferen",
			description:
				"Geef uw favoriete stoel, bank of fauteuil een tweede leven met nieuwe bekleding.",
		},
		{
			title: "Trapbekleding",
			description:
				"Vakkundig bekleden van uw trap met tapijt, voor een warme en veilige uitstraling.",
		},
		{
			title: "Vloerbedekking Leggen",
			description:
				"Professioneel leggen van tapijt en andere zachte vloerbedekking.",
		},
		{
			title: "Advies in Stoffen & Materialen",
			description:
				"Hulp bij het kiezen van de juiste stof die past bij uw interieur en levensstijl.",
		},
	];

	const benefits = [
		{
			icon: <Award className="w-8 h-8 text-white" />,
			title: "Ambachtelijk Vakwerk",
			description:
				"Onze stoffeerders combineren traditionele technieken met moderne materialen.",
		},
		{
			icon: <Sofa className="w-8 h-8 text-white" />,
			title: "Tweede Leven voor Meubels",
			description:
				"Herstofferen is een duurzame keuze en vaak voordeliger dan nieuw kopen.",
		},
		{
			icon: <Scissors className="w-8 h-8 text-white" />,
			title: "Grote Keuze in Stoffen",
			description:
				"Wij bieden een breed scala aan hoogwaardige meubelstoffen in vele kleuren en dessins.",
		},
	];

	const faqs = [
		{
			question: "Is herstofferen goedkoper dan een nieuw meubel kopen?",
			answer:
				"In veel gevallen wel, zeker als het gaat om een kwalitatief goed meubel. Herstofferen geeft u de kans om een uniek en persoonlijk meubelstuk te creëren.",
		},
		{
			question: "Hoe lang duurt het stofferen van een stoel?",
			answer:
				"Afhankelijk van de complexiteit van de stoel en de levertijd van de stof, duurt het proces gemiddeld 1 tot 3 weken.",
		},
		{
			question: "Welke stof is het meest slijtvast en kindvriendelijk?",
			answer:
				"Stoffen met een hoge Martindale-score (slijtvastheid) en materialen zoals microvezel of speciaal behandeld textiel zijn zeer geschikt voor intensief gebruik en makkelijk te reinigen.",
		},
		{
			question: "Kunnen jullie ook leer verwerken?",
			answer:
				"Jazeker. Onze stoffeerders hebben ervaring met het verwerken van zowel echt leer als kunstleer voor een luxe uitstraling.",
		},
	];

	const galleryMedia = [
		{ type: "image", url: "https://heyboss.heeyo.ai/1751741638-392d39b8.webp" },
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1556228453-efd6c1ff04f6?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1618220179428-22790b461013?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{ type: "image", url: "https://heyboss.heeyo.ai/1751741638-46ecf841.webp" },
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://heyboss.heeyo.ai/1751741638-392d39b8.webp"
							alt="Stoffeerder aan het werk met een meubelstuk"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Stoffeerder Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Geef uw meubels, trap of interieur een nieuwe, frisse
								uitstraling met professioneel stoffeerwerk.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een stoffeerder{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Stoffeerdiensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Ambachtelijk vakmanschap voor een duurzaam en prachtig
								resultaat.
							</p>
						</div>
						<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
							{upholsteryServices.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 p-8 rounded-2xl shadow-sm hover:shadow-xl hover:-translate-y-1 transition-all duration-300"
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600">{service.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Zekerheid van Klusgebied
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Kies voor een ervaren stoffeerder en geniet weer jaren van uw
								meubels.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-orange-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-orange-500 to-amber-600">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Is uw favoriete meubel versleten?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Gooi het niet weg! Vraag een vrijblijvende offerte aan voor
							herstoffering en geef het een tweede leven.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-orange-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Vraag nu een offerte aan
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_StoffeerderPage;

/**
 * @description This component renders a comprehensive and SEO-optimized detail page for carpenter services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for carpenters.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	Hammer,
	ShieldCheck,
	Clock,
	ArrowRight,
	TreePine,
} from "lucide-react";

const Service_TimmermanPage = () => {
	usePageTitle("Timmerman Nodig? | Klusgebied - Maatwerk & Houtbewerking");
	const navigate = useNavigate();

	const carpenterServices = [
		{
			title: "Maatwerk Meubels",
			description: "Kasten, tafels en meubels volledig op maat gemaakt.",
			longDescription:
				"Heeft u een specifieke wens voor een meubelstuk dat nergens te koop is? Onze timmerlieden zijn meesters in maatwerk. Wij ontwerpen en vervaardigen kasten, tafels, boekenkasten en andere meubels die perfect passen in uw interieur en voldoen aan al uw functionele eisen. We werken met diverse houtsoorten en zorgen voor een hoogwaardige afwerking.",
		},
		{
			title: "Deuren & Kozijnen",
			description: "Plaatsen en repareren van deuren en raamkozijnen.",
			longDescription:
				"Deuren en kozijnen zijn bepalend voor de uitstraling en isolatie van uw woning. Wij zijn gespecialiseerd in het plaatsen, vervangen en repareren van zowel binnen- als buitendeuren en -kozijnen. Of u nu kiest voor hout, kunststof of aluminium, wij zorgen voor een perfecte pasvorm, vakkundige montage en een tochtvrije afwerking.",
		},
		{
			title: "Houten Vloeren",
			description: "Leggen van parket en andere houten vloeren.",
			longDescription:
				"Een houten vloer geeft elke ruimte een warme en authentieke sfeer. Onze specialisten leggen diverse soorten houten vloeren, van massief parket en lamelparket tot visgraatpatronen. We zorgen voor een egale ondervloer, vakkundige plaatsing en een perfecte afwerking met olie, lak of was voor een duurzaam en prachtig resultaat.",
		},
		{
			title: "Dakconstructie",
			description: "Dakkapellen, zolderverbouwingen en dakbeschotten.",
			longDescription:
				"Wilt u meer ruimte creëren op zolder? Onze timmerlieden zijn ervaren in het realiseren van dakconstructies. Denk aan het plaatsen van een dakkapel voor extra licht en ruimte, het aftimmeren van een zolder tot een volwaardige kamer, of het repareren en vervangen van dakbeschot. Wij zorgen voor een solide en goed geïsoleerde constructie.",
		},
	];

	const benefits = [
		{
			icon: <ShieldCheck className="w-8 h-8 text-white" />,
			title: "Vakmanschap",
			description:
				"Jaren ervaring in alle aspecten van houtbewerking en timmerwerk.",
		},
		{
			icon: <Clock className="w-8 h-8 text-white" />,
			title: "Op Maat",
			description: "Elk project wordt speciaal voor u ontworpen en uitgevoerd.",
		},
		{
			icon: <TreePine className="w-8 h-8 text-white" />,
			title: "Duurzaam Hout",
			description: "Wij werken alleen met hoogwaardige, duurzame houtsoorten.",
		},
	];

	const faqs = [
		{
			question: "Wat kost maatwerk meubilair?",
			answer:
				"Dit hangt af van het ontwerp, houtsoort en afmetingen. Een eenvoudige kast begint rond €500, complexere projecten kunnen €2000+ kosten.",
		},
		{
			question: "Welke houtsoorten gebruiken jullie?",
			answer:
				"Wij werken met diverse houtsoorten zoals eiken, grenen, beuken en exotische houtsoorten, afhankelijk van uw wensen en budget.",
		},
		{
			question: "Kunnen jullie ook reparaties doen?",
			answer:
				"Jazeker, van kleine reparaties aan meubels tot het vervangen van beschadigde balken of planken.",
		},
		{
			question: "Hoe lang duurt een maatwerk project?",
			answer:
				"Een eenvoudig meubelstuk is binnen 1-2 weken klaar, grotere projecten zoals keukens kunnen 4-6 weken duren.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1645651964715-d200ce0939cc?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1618220179428-22790b461013?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=600",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1601391548091-de4ff62ee29c?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxjYXJwZW50ZXIlMjB3b3JraW5nJTJDJTIwd29vZHdvcmtpbmclMkMlMjBjcmFmdHNtYW5zaGlwfGVufDB8fHx8MTc1MTc0MDM0N3ww&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1705028877368-43d73100c1fd?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxjdXN0b20lMjBmdXJuaXR1cmUlMkMlMjBjYXJwZW50cnklMjBzZXJ2aWNlcyUyQyUyMGhvbWUlMjBpbXByb3ZlbWVudHxlbnwwfHx8fDE3NTE3NDAzNDd8MA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1645651964715-d200ce0939cc?ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Professionele timmerman aan het werk"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Timmerman Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Voor vakkundig timmerwerk en maatwerk meubels. Van kleine
								reparaties tot complete projecten.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een timmerman{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Timmer Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Vakmanschap in hout voor elke klus, van klein tot groot. Klik op
								een dienst voor meer informatie.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{carpenterServices.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								Waarom kiezen voor Klusgebied?
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Ervaren timmerlieden die staan voor kwaliteit en precisie.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-amber-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-amber-500 to-orange-500">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Heeft u een timmerklus?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Van maatwerk tot reparatie, onze timmermannen staan voor u klaar.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-amber-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Plaats nu je timmer klus
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_TimmermanPage;

/**
 * @description This component renders detailed service pages with comprehensive information, pricing, and professional listings for each service.
 * It dynamically displays content based on the service parameter from the URL, showing service descriptions, images, and call-to-action elements.
 * The component includes SEO-optimized content, responsive design, and animated elements for enhanced user engagement with service-specific targeting.
 * Key variables include serviceName from URL params, currentService object with service data, and professional listings for improved service targeting.
 */

import React, { useEffect, useState } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import { Helmet } from "react-helmet-async";
import {
	ArrowLeft,
	Star,
	CheckCircle,
	Clock,
	Euro,
	MapPin,
	Phone,
	Award,
	ShieldCheck,
	ThumbsUp,
	Wrench,
	Home,
	List,
	ArrowRight,
} from "lucide-react";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import servicesData from "../../lib/data/services.json";

const ServiceDetailPage = () => {
	const { serviceSlug } = useParams();
	const navigate = useNavigate();
	const [service, setService] = useState(null);
	const [relatedServices, setRelatedServices] = useState([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		const fetchAllData = async () => {
			setLoading(true);
			setService(null);
			setRelatedServices([]);

			try {
				// Fetch current service
				const currentServiceData = servicesData.find(
					(item) => item.slug === serviceSlug
				);

				if (!currentServiceData) {
					console.error("Error fetching service:", currentServiceData);
					navigate("/");
					return;
				}

				if (
					currentServiceData.seo_title &&
					currentServiceData.seo_title.length > 60
				) {
					console.warn(
						`SEO title for service "${currentServiceData.name}" is too long (${currentServiceData.seo_title.length} characters). Recommended max is 60.`
					);
				}
				if (
					currentServiceData.seo_description &&
					currentServiceData.seo_description.length > 155
				) {
					console.warn(
						`SEO description for service "${currentServiceData.name}" is too long (${currentServiceData.seo_description.length} characters). Recommended max is 155.`
					);
				}

				const serviceData = {
					...currentServiceData,
					faq: [
						{
							question: `Wat kost een ${currentServiceData.name.toLowerCase()}?`,
							answer:
								"De kosten hangen af van de omvang en complexiteit van de klus. Voor een precieze prijsopgave raden we aan om gratis en vrijblijvend een klus te plaatsen. U ontvangt dan offertes van onze geverifieerde vakmannen.",
						},
						{
							question: "Hoe snel kan een vakman beginnen?",
							answer:
								"Voor spoedklussen kan er vaak al binnen enkele uren een professional ter plaatse zijn. Voor reguliere projecten kunt u in overleg met de vakman een startdatum plannen die u het beste uitkomt.",
						},
						{
							question: "Zijn de aangesloten professionals betrouwbaar?",
							answer:
								"Ja, alle vakmannen op Klusgebied worden door ons gescreend. We controleren inschrijvingen bij de Kamer van Koophandel, certificeringen en reviews om de kwaliteit en betrouwbaarheid te waarborgen.",
						},
					],
				};
				setService(serviceData);

				// Fetch related services
				const otherServices = servicesData.filter(
					(item) => item.slug !== serviceSlug
				);

				if (otherServices) {
					const sameCategory = otherServices.filter(
						(s) =>
							s.category === serviceData.category && s.slug !== serviceData.slug
					);
					const differentCategory = otherServices.filter(
						(s) =>
							s.category !== serviceData.category && s.slug !== serviceData.slug
					);

					const shuffledRelated = [...sameCategory, ...differentCategory]
						.sort(() => 0.5 - Math.random())
						.slice(0, 5);

					setRelatedServices(shuffledRelated);
				}
			} catch (error) {
				console.error("Error:", error);
				navigate("/");
			} finally {
				setLoading(false);
			}
		};

		if (serviceSlug) {
			fetchAllData();
		}
	}, [serviceSlug, navigate]);

	if (loading) {
		return (
			<div className="min-h-screen bg-white flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-500 mx-auto"></div>
					<p className="mt-4 text-slate-600">Laden...</p>
				</div>
			</div>
		);
	}

	if (!service) {
		return (
			<div className="min-h-screen bg-white">
				<Header />
				<div className="pt-20 pb-16 text-center">
					<h1 className="text-2xl font-bold text-slate-800 mb-4">
						Service niet gevonden
					</h1>
					<button
						onClick={() => navigate("/")}
						className="bg-teal-500 text-white px-6 py-3 rounded-xl font-semibold hover:bg-teal-600 transition-colors"
					>
						Terug naar home
					</button>
				</div>
				<Footer />
			</div>
		);
	}

	// Mock data for professionals (since we don't have a professionals table yet)
	const professionals = [
		{
			name: "Jan van der Berg",
			rating: 4.9,
			reviews: 127,
			experience: "8+ jaar ervaring",
			location: "Amsterdam",
			phone: "06-12345678",
			specialties: ["Spoedklussen", "Weekendservice"],
			image:
				"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.1.0&w=400&h=400",
		},
		{
			name: "Maria Janssen",
			rating: 4.8,
			reviews: 98,
			experience: "6+ jaar ervaring",
			location: "Utrecht",
			phone: "06-87654321",
			specialties: ["Garantie werk", "Gratis advies"],
			image:
				"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.1.0&w=400&h=400",
		},
		{
			name: "Pieter de Vries",
			rating: 4.7,
			reviews: 156,
			experience: "10+ jaar ervaring",
			location: "Rotterdam",
			phone: "06-11223344",
			specialties: ["24/7 service", "Eigen materiaal"],
			image:
				"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.1.0&w=400&h=400",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			{service && (
				<Helmet>
					<title>{service.seo_title || `${service.name} | Klusgebied`}</title>
					<meta
						name="description"
						content={
							service.seo_description ||
							`Vind snel en eenvoudig een geverifieerde ${service.name.toLowerCase()} in jouw regio.`
						}
					/>
					<link
						rel="canonical"
						href={`https://www.klusgebied.nl/#/diensten/${service.slug}`}
					/>
					<meta
						property="og:title"
						content={service.seo_title || `${service.name} | Klusgebied`}
					/>
					<meta
						property="og:description"
						content={
							service.seo_description ||
							`Vind snel en eenvoudig een geverifieerde ${service.name.toLowerCase()} in jouw regio.`
						}
					/>
					<meta
						property="og:image"
						content={
							service.image_url ||
							"https://heyboss.heeyo.ai/chat-images/ChatGPT%20Image%2025%20jun%202025,%2015_48_09_32j6tyj5.png"
						}
					/>
					<meta name="twitter:card" content="summary_large_image" />
					<script type="application/ld+json">
						{JSON.stringify({
							"@context": "https://schema.org",
							"@type": "Service",
							name: service.name,
							serviceType: service.name,
							description:
								service.seo_description ||
								`Vind snel en eenvoudig een geverifieerde ${service.name.toLowerCase()} in jouw regio.`,
							provider: {
								"@type": "LocalBusiness",
								name: "Klusgebied",
								image:
									"https://heyboss.heeyo.ai/chat-images/ChatGPT%20Image%2025%20jun%202025,%2015_48_09_32j6tyj5.png",
								url: "https://www.klusgebied.nl/",
								telephone: "+31-85-1234567",
								priceRange: "€€",
								address: {
									"@type": "PostalAddress",
									streetAddress: "Kabelweg 43",
									addressLocality: "Amsterdam",
									postalCode: "1014 BA",
									addressCountry: "NL",
								},
							},
							areaServed: {
								"@type": "Country",
								name: "Netherlands",
							},
						})}
					</script>
					{service.faq && service.faq.length > 0 && (
						<script type="application/ld+json">
							{JSON.stringify({
								"@context": "https://schema.org",
								"@type": "FAQPage",
								mainEntity: service.faq.map((q) => ({
									"@type": "Question",
									name: q.question,
									acceptedAnswer: {
										"@type": "Answer",
										text: q.answer,
									},
								})),
							})}
						</script>
					)}
				</Helmet>
			)}
			<Header />

			{/* Hero Section */}
			<section className="relative pt-20 lg:pt-24 pb-16 overflow-hidden">
				<div className="absolute inset-0">
					<img
						src={
							service.image_url ||
							"https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-4.1.0&w=1024&h=1024"
						}
						alt={`${service.name} vakmannen`}
						className="w-full h-full object-cover"
						fetchPriority="high"
						loading="lazy"
					/>
					<div className="absolute inset-0 bg-gradient-to-r from-slate-900/80 to-slate-900/40"></div>
				</div>

				<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<button
						onClick={() => navigate("/")}
						className="flex items-center space-x-2 text-white/80 hover:text-white mb-8 transition-all duration-300 hover:scale-105"
					>
						<ArrowLeft className="w-5 h-5" />
						<span className="font-medium">Terug naar overzicht</span>
					</button>

					<div className="max-w-4xl">
						<h1 className="text-4xl lg:text-6xl font-bold text-white mb-6 motion-preset-slide-up">
							{service.name} nodig?
						</h1>
						<p className="text-xl lg:text-2xl text-slate-200 mb-12 leading-relaxed motion-preset-slide-up motion-delay-200">
							{service.seo_description ||
								`Vind snel en eenvoudig een geverifieerde ${service.name.toLowerCase()} in jouw regio. Ervaren professionals die klaarstaan om uw klus vakkundig uit te voeren.`}
						</p>

						<div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-8 motion-preset-slide-up motion-delay-400">
							<div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/20">
								<Wrench className="w-8 h-8 text-teal-400 mx-auto mb-2" />
								<div className="text-2xl font-bold text-white">150+</div>
								<div className="text-slate-300 text-sm">Professionals</div>
							</div>
							<div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/20">
								<CheckCircle className="w-8 h-8 text-green-400 mx-auto mb-2" />
								<div className="text-2xl font-bold text-white">2.4K+</div>
								<div className="text-slate-300 text-sm">Klussen</div>
							</div>
							<div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/20">
								<Star className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
								<div className="text-2xl font-bold text-white">4.8</div>
								<div className="text-slate-300 text-sm">Beoordeling</div>
							</div>
							<div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/20">
								<Clock className="w-8 h-8 text-blue-400 mx-auto mb-2" />
								<div className="text-2xl font-bold text-white">&lt; 2u</div>
								<div className="text-slate-300 text-sm">Reactietijd</div>
							</div>
						</div>

						<button
							onClick={() => {
								if (service && service.slug) {
									// Map slugs for external redirect
									let targetSlug = service.slug;
									if (targetSlug === "elektricien") {
										targetSlug = "elektra-klussen";
									} else if (targetSlug === "loodgieter") {
										targetSlug = "waterleiding-vervangen";
									} else if (targetSlug === "schilder") {
										targetSlug = "stucwerk-binnen";
									}
									window.open(
										`https://klusgebied.nl/plaats-een-klus/${targetSlug}`,
										"_blank",
										"noopener,noreferrer"
									);
								}
							}}
							className="bg-teal-500 text-white px-8 py-4 rounded-2xl font-bold text-xl hover:bg-teal-600 transition-all duration-300 shadow-md hover:shadow-lg hover:-translate-y-0.5"
						>
							Plaats direct een {service.name.toLowerCase()} klus
						</button>
					</div>
				</div>
			</section>

			{/* Service Description Section */}
			{(service.full_content_html || service.rich_description) && (
				<section className="py-16 lg:py-20 bg-white">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
						<div
							className="prose prose-lg max-w-none text-slate-700 leading-relaxed"
							dangerouslySetInnerHTML={{
								__html: service.full_content_html || service.rich_description,
							}}
						/>

						{/* Related Services Section */}
						{relatedServices.length > 0 && (
							<div className="mt-16 pt-12 border-t border-slate-200">
								<h3 className="text-2xl font-bold text-slate-800 mb-6">
									Gerelateerde diensten
								</h3>
								<ul className="space-y-3">
									{relatedServices.map((related) => (
										<li key={related.slug}>
											<Link
												to={`/diensten/${related.slug}`}
												className="flex items-center text-teal-600 hover:text-teal-700 hover:underline font-medium group transition-all"
											>
												<ArrowRight className="w-4 h-4 mr-3 text-teal-500 group-hover:translate-x-1 transition-transform" />
												<span>{related.name}</span>
											</Link>
										</li>
									))}
								</ul>
								<div className="mt-8">
									<Link
										to="/diensten"
										className="inline-flex items-center bg-slate-100 text-slate-700 px-5 py-3 rounded-xl font-semibold hover:bg-slate-200 transition-colors"
									>
										<List className="w-5 h-5 mr-2" />
										Bekijk alle diensten
									</Link>
								</div>
							</div>
						)}
					</div>
				</section>
			)}

			{/* Professionals Section */}
			<section className="py-16 lg:py-20 bg-slate-50">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<h2 className="text-3xl lg:text-4xl font-bold text-slate-800 text-center mb-4">
						Top {service.name} professionals
					</h2>
					<p className="text-lg text-slate-600 text-center mb-12 max-w-3xl mx-auto">
						Ontdek onze best beoordeelde {service.name.toLowerCase()}{" "}
						professionals. Alle vakmannen zijn geverifieerd en hebben bewezen
						ervaring.
					</p>

					<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
						{professionals.map((professional, index) => (
							<div
								key={professional.name}
								className={`bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-100 motion-preset-slide-up motion-delay-${
									index * 100
								}`}
							>
								<div className="flex items-center mb-6">
									<img
										src={professional.image}
										alt={professional.name}
										className="w-16 h-16 rounded-full object-cover mr-4"
										loading="lazy"
										width="64"
										height="64"
									/>
									<div>
										<h3 className="text-xl font-bold text-slate-800">
											{professional.name}
										</h3>
										<div className="flex items-center space-x-2 text-sm text-slate-600">
											<MapPin className="w-4 h-4" />
											<span>{professional.location}</span>
										</div>
									</div>
								</div>

								<div className="flex items-center mb-4">
									<div className="flex">
										{[...Array(5)].map((_, i) => (
											<Star
												key={i}
												className={`w-5 h-5 ${
													i < Math.floor(professional.rating)
														? "text-yellow-400 fill-current"
														: "text-slate-300"
												}`}
											/>
										))}
									</div>
									<span className="ml-2 text-sm text-slate-600">
										{professional.rating} ({professional.reviews} reviews)
									</span>
								</div>

								<p className="text-slate-600 mb-4">{professional.experience}</p>

								<div className="flex flex-wrap gap-2 mb-6">
									{professional.specialties.map((specialty) => (
										<span
											key={specialty}
											className="bg-teal-100 text-teal-700 px-3 py-1 rounded-full text-sm font-medium"
										>
											{specialty}
										</span>
									))}
								</div>

								<div className="flex space-x-3">
									<button className="flex-1 bg-teal-500 text-white py-3 rounded-xl font-semibold hover:bg-teal-600 transition-colors">
										Contact
									</button>
									<button className="flex items-center justify-center bg-slate-100 text-slate-700 p-3 rounded-xl hover:bg-slate-200 transition-colors">
										<Phone className="w-5 h-5" />
									</button>
								</div>
							</div>
						))}
					</div>
				</div>
			</section>

			{/* Benefits Section */}
			<section className="py-16 lg:py-20 bg-white">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<h2 className="text-3xl lg:text-4xl font-bold text-slate-800 text-center mb-12">
						Waarom kiezen voor Klusgebied?
					</h2>

					<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
						<div className="text-center">
							<div className="bg-teal-500/10 p-4 rounded-2xl w-16 h-16 mx-auto mb-4 flex items-center justify-center">
								<ShieldCheck className="w-8 h-8 text-teal-500" />
							</div>
							<h3 className="text-xl font-bold text-slate-800 mb-2">
								Geverifieerd
							</h3>
							<p className="text-slate-600">
								Alle professionals zijn gescreend en geverifieerd
							</p>
						</div>

						<div className="text-center">
							<div className="bg-teal-500/10 p-4 rounded-2xl w-16 h-16 mx-auto mb-4 flex items-center justify-center">
								<Award className="w-8 h-8 text-teal-500" />
							</div>
							<h3 className="text-xl font-bold text-slate-800 mb-2">
								Kwaliteit
							</h3>
							<p className="text-slate-600">
								Hoge kwaliteit werk met tevredenheidsgarantie
							</p>
						</div>

						<div className="text-center">
							<div className="bg-teal-500/10 p-4 rounded-2xl w-16 h-16 mx-auto mb-4 flex items-center justify-center">
								<Clock className="w-8 h-8 text-teal-500" />
							</div>
							<h3 className="text-xl font-bold text-slate-800 mb-2">Snel</h3>
							<p className="text-slate-600">
								Snelle reactietijd en flexibele planning
							</p>
						</div>

						<div className="text-center">
							<div className="bg-teal-500/10 p-4 rounded-2xl w-16 h-16 mx-auto mb-4 flex items-center justify-center">
								<ThumbsUp className="w-8 h-8 text-teal-500" />
							</div>
							<h3 className="text-xl font-bold text-slate-800 mb-2">
								Betrouwbaar
							</h3>
							<p className="text-slate-600">
								Transparante prijzen en betrouwbare service
							</p>
						</div>
					</div>
				</div>
			</section>

			{/* Final CTA Section */}
			<section className="py-16 lg:py-20 bg-gradient-to-r from-teal-500 to-blue-600">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
					<h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
						Klaar voor uw {service.name.toLowerCase()} klus?
					</h2>
					<p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
						Plaats uw klus gratis en ontvang binnen 24 uur offertes van
						gekwalificeerde {service.name.toLowerCase()} professionals in uw
						regio.
					</p>

					<button
						onClick={() => {
							if (service && service.slug) {
								// Map slugs for external redirect
								let targetSlug = service.slug;
								if (targetSlug === "elektricien") {
									targetSlug = "elektra-klussen";
								} else if (targetSlug === "loodgieter") {
									targetSlug = "waterleiding-vervangen";
								} else if (targetSlug === "schilder") {
									targetSlug = "stucwerk-binnen";
								}
								window.open(
									`https://klusgebied.nl/plaats-een-klus/${targetSlug}`,
									"_blank",
									"noopener,noreferrer"
								);
							}
						}}
						className="bg-white text-teal-600 px-8 py-4 rounded-2xl font-bold text-xl hover:bg-slate-50 transition-all duration-300 shadow-lg hover:shadow-xl"
					>
						Plaats nu uw {service.name.toLowerCase()} klus
					</button>

					<div className="mt-8 text-white/80 text-sm">
						✅ Gratis plaatsen • 🔒 100% veilig • ⚡ Snelle reactie
					</div>
				</div>
			</section>

			<Footer />
		</div>
	);
};

export default ServiceDetailPage;

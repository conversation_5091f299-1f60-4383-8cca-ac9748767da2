/**
 * @description This component renders a comprehensive and SEO-optimized detail page for gardener services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for gardeners.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	TreePine,
	ShieldCheck,
	Clock,
	ArrowRight,
	Leaf,
} from "lucide-react";

const Service_TuinmanPage = () => {
	usePageTitle("Tuinman Nodig? | Klusgebied - Tuinonderhoud & Tuinaanleg");
	const navigate = useNavigate();

	const gardenerServices = [
		{
			title: "Tuinonderhoud",
			description:
				"Regulier onderhoud om uw tuin het hele jaar mooi te houden.",
			longDescription:
				"Een mooie tuin vraagt om regelmatig onderhoud. Onze tuinmannen nemen dit werk graag van u over. Wij bieden onderhoudscontracten op maat, van wekelijks tot seizoensgebonden onderhoud. Denk aan grasmaaien, onkruid wieden, hagen snoeien en het bemesten van uw planten. Zo ligt uw tuin er altijd verzorgd bij, zonder dat u er zelf omkijken naar heeft.",
		},
		{
			title: "Tuinaanleg",
			description: "Complete nieuwe tuinen ontwerpen en aanleggen.",
			longDescription:
				"Droomt u van een compleet nieuwe tuin? Onze hoveniers helpen u van ontwerp tot realisatie. We bespreken uw wensen en creëren een tuinontwerp dat past bij uw stijl en de mogelijkheden van de ruimte. Vervolgens verzorgen we de volledige aanleg, inclusief grondwerk, bestrating, beplanting, en eventueel de aanleg van een vijver of gazon.",
		},
		{
			title: "Snoeien & Kappen",
			description: "Professioneel snoeien van bomen en struiken.",
			longDescription:
				"Snoeien is essentieel voor de gezondheid en vorm van bomen en struiken. Onze specialisten weten precies wanneer en hoe ze moeten snoeien voor een optimale groei en bloei. Ook voor het veilig kappen of verwijderen van bomen kunt u bij ons terecht. Wij beschikken over de juiste kennis en materialen om dit veilig en efficiënt uit te voeren.",
		},
		{
			title: "Bestrating & Terrassen",
			description: "Aanleg van paden, terrassen en verhardingen.",
			longDescription:
				"Een mooi terras of een praktisch tuinpad is de basis van een functionele tuin. Wij zijn gespecialiseerd in het aanleggen van alle soorten bestrating, van sierbestrating en terrastegels tot opritten. We zorgen voor een stevige ondergrond om verzakking te voorkomen en leggen de stenen of tegels in het door u gewenste patroon voor een strak en duurzaam resultaat.",
		},
	];

	const benefits = [
		{
			icon: <ShieldCheck className="w-8 h-8 text-white" />,
			title: "Seizoensservice",
			description: "Wij houden uw tuin het hele jaar door in topconditie.",
		},
		{
			icon: <Clock className="w-8 h-8 text-white" />,
			title: "Flexibele Planning",
			description: "Onderhoud op vaste tijden of op afroep.",
		},
		{
			icon: <Leaf className="w-8 h-8 text-white" />,
			title: "Duurzaam Tuinieren",
			description: "Milieuvriendelijke aanpak en plantenkeuze.",
		},
	];

	const faqs = [
		{
			question: "Wat kost tuinonderhoud per maand?",
			answer:
				"Maandelijks onderhoud kost tussen €50-150 per beurt, afhankelijk van de grootte en intensiteit van het onderhoud.",
		},
		{
			question: "Wanneer is het beste seizoen voor tuinaanleg?",
			answer:
				"Het voorjaar (maart-mei) en de herfst (september-november) zijn ideaal voor het planten en aanleggen van tuinen.",
		},
		{
			question: "Kunnen jullie ook tuinontwerp doen?",
			answer:
				"Ja, onze tuinmannen hebben ervaring met tuinontwerp en kunnen een complete nieuwe tuin voor u ontwerpen.",
		},
		{
			question: "Hoe vaak moet mijn tuin onderhouden worden?",
			answer:
				"Dit hangt af van uw tuin, maar gemiddeld is 1x per maand onderhoud voldoende om alles netjes te houden.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1523348837708-15d4a09cfac2?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1750762286053-28632f48e717?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxnYXJkZW4lMjBkZXNpZ24lMkMlMjBsYW5kc2NhcGluZyUyQyUyMG91dGRvb3IlMjBzcGFjZXxlbnwwfHx8fDE3NTE1MDgwODJ8MA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1558904541-efa843a96f01?ixlib=rb-4.1.0&w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1523348837708-15d4a09cfac2?ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Professionele tuinman aan het werk"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/60"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Tuinman Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Voor professioneel tuinonderhoud en complete tuinaanleg. Maak
								van uw tuin een groene oase.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een tuinman <ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Tuinman Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Van onderhoud tot complete herinrichting van uw buitenruimte.
								Klik op een dienst voor meer informatie.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{gardenerServices.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								Waarom kiezen voor Klusgebied?
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Ervaren tuinmannen die uw droomtuin werkelijkheid maken.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-green-500 to-emerald-500">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Droomt u van een mooie tuin?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Onze ervaren tuinmannen maken van uw buitenruimte een groene oase.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-green-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Plaats nu je tuinklus
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_TuinmanPage;

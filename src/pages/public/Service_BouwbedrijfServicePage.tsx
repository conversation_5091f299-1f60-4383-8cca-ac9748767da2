/**
 * @description This component renders a comprehensive and SEO-optimized detail page for construction company services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for construction companies.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	ArrowRight,
	Construction,
	Building,
	ShieldCheck,
} from "lucide-react";

const Service_BouwbedrijfServicePage = () => {
	usePageTitle("Bouwbedrijf Nodig? | Klusgebied - Voor Nieuwbouw & Renovatie");
	const navigate = useNavigate();

	const services = [
		{
			title: "Nieuwbouwprojecten",
			description:
				"Realisatie van uw droomhuis of bedrijfspand van de eerste paal tot de laatste steen.",
			longDescription:
				"Wij bouwen uw droomhuis of bedrijfspand volledig naar uw wensen. Vanaf het eerste ontwerp en de vergunningsaanvraag tot de sleutelklare oplevering. Wij werken met een vast team van specialisten en garanderen de hoogste kwaliteit.",
		},
		{
			title: "Grote Renovaties",
			description:
				"Complete renovatie en modernisering van woningen en commercieel vastgoed.",
			longDescription:
				"Is uw woning of pand toe aan een grondige update? Wij zijn gespecialiseerd in grootschalige renovaties. We combineren moderne technieken met respect voor de bestaande architectuur om een duurzaam en waardevol resultaat te realiseren.",
		},
		{
			title: "Utiliteitsbouw",
			description:
				"Bouw van functionele gebouwen zoals kantoren, scholen en zorginstellingen.",
			longDescription:
				"Naast woningbouw zijn we ook actief in de utiliteitsbouw. Wij bouwen functionele en duurzame gebouwen voor de zakelijke markt, overheid en zorgsector, waarbij we altijd rekening houden met de specifieke eisen van de gebruiker.",
		},
		{
			title: "Duurzaam Bouwen",
			description:
				"Toepassing van duurzame materialen en energiezuinige technieken.",
			longDescription:
				"Bouwen voor de toekomst doen we samen. Wij adviseren en implementeren duurzame oplossingen zoals warmtepompen, zonnepanelen, hoogwaardige isolatie en circulaire materialen om een energiezuinig en milieuvriendelijk gebouw te realiseren.",
		},
	];

	const benefits = [
		{
			icon: <Construction className="w-8 h-8 text-white" />,
			title: "Ervaren Team",
			description:
				"Ons team bestaat uit vakkundige timmerlieden, metselaars en andere specialisten.",
		},
		{
			icon: <ShieldCheck className="w-8 h-8 text-white" />,
			title: "Kwaliteit & Garantie",
			description:
				"Wij staan voor de hoogste kwaliteit en bieden garantie op al ons werk.",
		},
		{
			icon: <Building className="w-8 h-8 text-white" />,
			title: "Betrouwbare Partner",
			description:
				"Een solide en betrouwbare partner voor uw complete bouwproject.",
		},
	];

	const faqs = [
		{
			question: "Wat is het verschil tussen een aannemer en een bouwbedrijf?",
			answer:
				"Een bouwbedrijf heeft vaak eigen personeel voor diverse specialisaties, terwijl een aannemer vaker onderaannemers inhuurt. Voor grote, complexe projecten is een bouwbedrijf vaak een logische keuze.",
		},
		{
			question: "Hoe ziet het bouwproces eruit?",
			answer:
				"Het proces omvat doorgaans: ontwerp & vergunning, voorbereiding, ruwbouw (fundering, muren, dak), afbouw (installaties, afwerking) en oplevering.",
		},
		{
			question: "Met welke keurmerken werken jullie?",
			answer:
				"Wij werken met erkende keurmerken zoals BouwGarant en Woningborg, wat u zekerheid biedt over kwaliteit en financiële afhandeling.",
		},
		{
			question: "Kunnen jullie helpen met de bouwvergunning?",
			answer:
				"Jazeker. Wij kunnen het volledige vergunningstraject voor u begeleiden, van de tekeningen tot de aanvraag bij de gemeente.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1637044875244-6a002ad32799?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxjb25zdHJ1Y3Rpb24lMjBzaXRlJTJDJTIwYnVpbGRpbmclMjBjcmFuZSUyQyUyMHVuZGVyJTIwY29uc3RydWN0aW9ufGVufDB8fHx8MTc1MTc0MjQyNHww&ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1665069181618-5618c9b621ec?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxyZW5vdmF0aW9uJTIwcHJvamVjdCUyQyUyMGNvbnN0cnVjdGlvbiUyMHdvcmtlcnMlMkMlMjBob21lJTIwaW1wcm92ZW1lbnR8ZW58MHx8fHwxNzUxNzQyNDI0fDA&ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1487958449943-2429e8be8625?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1612043548602-0e66563b0ec9?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxtb2Rlcm4lMjBhcmNoaXRlY3R1cmUlMkMlMjBidWlsZGluZyUyMGRlc2lnbiUyQyUyMGNvbnN0cnVjdGlvbiUyMG1hdGVyaWFsc3xlbnwwfHx8fDE3NTE3NDI0MjR8MA&ixlib=rb-4.1.0&w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1637044875244-6a002ad32799?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxjb25zdHJ1Y3Rpb24lMjBzaXRlJTJDJTIwYnVpbGRpbmclMjBjcmFuZSUyQyUyMHVuZGVyJTIwY29uc3RydWN0aW9ufGVufDB8fHx8MTc1MTc0MjQyNHww&ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Bouwplaats met een kraan en een gebouw in aanbouw"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Bouwbedrijf Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Uw betrouwbare partner voor nieuwbouw, verbouw en grootschalige
								renovatieprojecten.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Start uw bouwproject{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Bouwdiensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Vakmanschap en expertise voor elk type bouwproject.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{services.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Zekerheid van een Bouwbedrijf
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Kies voor een solide partner die kwaliteit, betrouwbaarheid en
								vakmanschap garandeert.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-gray-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				<section className="bg-gradient-to-r from-gray-700 to-slate-800">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Klaar om uw droom te bouwen?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Neem contact op voor een vrijblijvend gesprek over uw nieuwbouw-
							of renovatieplannen.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-slate-800 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Neem contact op
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_BouwbedrijfServicePage;

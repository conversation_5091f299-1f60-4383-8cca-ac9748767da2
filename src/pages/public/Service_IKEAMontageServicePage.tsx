/**
 * @description This component renders a comprehensive and SEO-optimized detail page for IKEA assembly services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for IKEA furniture assembly.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	ArrowRight,
	PackageCheck,
	Clock,
	ThumbsUp,
} from "lucide-react";

const Service_IKEAMontageServicePage = () => {
	usePageTitle("IKEA Montage Service | Klusgebied - Snel & Vakkundig");
	const navigate = useNavigate();

	const services = [
		{
			title: "PAX Kasten Monteren",
			description:
				"Vakkundige montage van uw complete PAX garderobekast, inclusief deuren en interieur.",
			longDescription:
				"De PAX kast is een fantastisch systeem, maar de montage kan een uitdaging zijn. Onze monteurs hebben honderden PAX kasten gemonteerd. Wij zorgen voor een snelle, stevige en waterpas montage van de kasten, deuren en alle interieuraccessoires zoals lades en planken.",
		},
		{
			title: "BESTÅ Kasten Monteren",
			description:
				"Montage van BESTÅ tv-meubels, opbergkasten en complete wandcombinaties.",
			longDescription:
				"Het BESTÅ systeem biedt eindeloze mogelijkheden voor uw woonkamer. Wij monteren uw tv-meubel of complete wandkast precies zoals u het heeft ontworpen. We zorgen ook voor een stevige en veilige wandmontage indien gewenst.",
		},
		{
			title: "METOD Keukens Installeren",
			description:
				"Volledige installatie van uw IKEA keuken, van kasten tot werkblad en apparatuur.",
			longDescription:
				"Een IKEA keuken installeren is een complex project. Onze ervaren keukenmonteurs nemen u al het werk uit handen. Wij monteren de kasten, zagen en plaatsen het werkblad, sluiten de apparatuur aan en zorgen voor een perfect afgewerkt eindresultaat.",
		},
		{
			title: "Algemene Meubelmontage",
			description:
				"Montage van bedden, bureaus, tafels, stoelen en andere IKEA meubels.",
			longDescription:
				"Heeft u andere IKEA meubels gekocht? Van een MALM bed tot een MICKE bureau of een KALLAX kast, wij monteren het snel en vakkundig voor u. Bespaar uzelf de tijd en frustratie en laat het over aan een professional.",
		},
	];

	const benefits = [
		{
			icon: <Clock className="w-8 h-8 text-white" />,
			title: "Snel & Efficiënt",
			description:
				"Onze monteurs kennen de producten en monteren uw meubels snel en correct.",
		},
		{
			icon: <ThumbsUp className="w-8 h-8 text-white" />,
			title: "Geen Stress",
			description:
				"Bespaar uzelf de tijd en frustratie van ingewikkelde handleidingen.",
		},
		{
			icon: <PackageCheck className="w-8 h-8 text-white" />,
			title: "Perfect Resultaat",
			description:
				"Wij zorgen voor een stevig en perfect afgesteld meubel, klaar voor gebruik.",
		},
	];

	const faqs = [
		{
			question: "Wat kost het om IKEA meubels te laten monteren?",
			answer:
				"De kosten zijn meestal gebaseerd op een uurtarief (gemiddeld €45-€65) of een vaste prijs per meubelstuk. Voor een grote PAX kast kunt u rekenen op 2-4 uur werk.",
		},
		{
			question: "Moet ik zelf aanwezig zijn tijdens de montage?",
			answer:
				"Het is handig als u aanwezig bent om de monteur binnen te laten en eventuele vragen over de plaatsing te beantwoorden. Daarna kunt u uw gang gaan.",
		},
		{
			question: "Wat moet ik voorbereiden voor de monteur komt?",
			answer:
				"Zorg ervoor dat de dozen in de juiste kamer staan en er voldoende werkruimte is. Controleer of alle pakketten compleet zijn.",
		},
		{
			question: "Monteren jullie ook meubels van andere merken?",
			answer:
				"Jazeker! Hoewel we gespecialiseerd zijn in IKEA, monteren onze klusjesmannen ook meubels van andere merken.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1653971858474-4f2dfa7f4dc1?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxJS0VBJTIwYXNzZW1ibHklMkMlMjBmdXJuaXR1cmUlMjBhc3NlbWJseSUyQyUyMGhvbWUlMjBpbXByb3ZlbWVudHxlbnwwfHx8fDE3NTE3NDI0Mzl8MA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1602123116122-9b6e1695a670?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxJS0VBJTIwZnVybml0dXJlJTJDJTIwcHJvZmVzc2lvbmFsJTIwYXNzZW1ibHklMkMlMjBob21lJTIwZGVjb3J8ZW58MHx8fHwxNzUxNzQyNDM5fDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1556228453-efd6c1ff04f6?ixlib=rb-4.1.0&w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Een persoon die een IKEA meubel in elkaar zet"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								IKEA Montage Hulp Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Geen zin in gedoe met handleidingen en schroefjes? Onze ervaren
								monteurs zetten uw IKEA meubels snel en vakkundig in elkaar.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Boek een monteur <ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze IKEA Montage Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Voor elk IKEA meubel een snelle en vakkundige montage.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{services.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Voordelen van Montagehulp
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Kies voor gemak, snelheid en een perfect gemonteerd meubel.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-blue-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				<section className="bg-gradient-to-r from-blue-500 to-cyan-500">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Klaar met de montage-stress?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Plaats uw klus en laat uw meubels monteren door een professional.
							Geniet direct van uw aankoop.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-blue-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Plaats nu uw montageklus
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_IKEAMontageServicePage;

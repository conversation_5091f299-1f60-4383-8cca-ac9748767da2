/**
 * @description This component renders a comprehensive and SEO-optimized detail page for parquet flooring services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for parquet specialists.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	ArrowRight,
	ShieldCheck,
	Award,
	Layers,
} from "lucide-react";

const Service_ParketvloerSpecialistPage = () => {
	usePageTitle(
		"Parketvloer Specialist Nodig? | Klusgebied - Leggen, Schuren & Lakken"
	);
	const navigate = useNavigate();

	const services = [
		{
			title: "Traditioneel Parket Leggen",
			description:
				"Massief houten vloeren, gelegd in patronen zoals visgraat of Hongaarse punt.",
			longDescription:
				"Voor de ultieme luxe en authenticiteit kiest u voor traditioneel parket. Deze massieve houten plankjes worden ter plekke verlijmd en genageld, geschuurd en afgewerkt. Dit maakt prachtige patronen zoals visgraat mogelijk.",
		},
		{
			title: "Lamellenparket (Multiplank)",
			description:
				"De uitstraling van een massieve vloer met de stabiliteit van een gelaagde opbouw.",
			longDescription:
				"Lamellenparket, ook wel multiplank genoemd, bestaat uit een toplaag van edelhout op een stabiele onderlaag. Dit combineert de look van een massieve vloer met minder werking, waardoor het ook geschikt is voor vloerverwarming.",
		},
		{
			title: "Parket Schuren & Lakken",
			description:
				"Uw bestaande houten vloer weer als nieuw maken door schuren en afwerken.",
			longDescription:
				"Is uw parketvloer bekrast, versleten of verkleurd? Door de vloer professioneel te schuren en opnieuw te behandelen met lak, olie of hardwax, ziet deze er weer als nieuw uit. Een duurzame manier om uw vloer een tweede leven te geven.",
		},
		{
			title: "Vloeronderhoud & Reparatie",
			description:
				"Periodiek onderhoud en reparatie van beschadigingen aan uw parketvloer.",
			longDescription:
				"Om uw houten vloer mooi te houden is periodiek onderhoud belangrijk. Wij kunnen uw vloer diep reinigen en een nieuwe beschermlaag aanbrengen. Ook voor het repareren van krassen of waterschade bent u bij ons aan het juiste adres.",
		},
	];

	const benefits = [
		{
			icon: <Award className="w-8 h-8 text-white" />,
			title: "Ambachtelijk Vakmanschap",
			description:
				"Onze specialisten hebben oog voor detail en leveren perfect afgewerkte vloeren.",
		},
		{
			icon: <ShieldCheck className="w-8 h-8 text-white" />,
			title: "Stofvrij Schuren",
			description:
				"Wij gebruiken moderne apparatuur voor een schone en stofvrije werkomgeving.",
		},
		{
			icon: <Layers className="w-8 h-8 text-white" />,
			title: "Duurzame Materialen",
			description:
				"Wij werken met hoogwaardige, duurzame houtsoorten en lakken.",
		},
	];

	const faqs = [
		{
			question: "Wat kost een nieuwe parketvloer?",
			answer:
				"De prijs is afhankelijk van de houtsoort en het legpatroon, en varieert van €40 tot €120 per m². Vraag een offerte aan voor een exacte prijs.",
		},
		{
			question: "Hoe lang duurt het schuren van een vloer?",
			answer:
				"Het schuren en lakken van een gemiddelde woonkamer (ca. 40m²) duurt meestal 2 tot 3 dagen, inclusief droogtijd.",
		},
		{
			question: "Welk onderhoud heeft een parketvloer nodig?",
			answer:
				"Regelmatig stofzuigen en dweilen met een speciaal parket-reinigingsmiddel is voldoende. Periodiek onderhoud met olie of was kan nodig zijn.",
		},
		{
			question: "Is parket geschikt voor vloerverwarming?",
			answer:
				"Ja, veel soorten parket zijn geschikt voor vloerverwarming. Het is belangrijk om de juiste houtsoort en ondervloer te kiezen. Onze specialisten adviseren u graag.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1712038487428-7f7f5d76b69b?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxwYXJrZXR2bG9lciUyQyUyMHZpc2dyYWF0JTJDJTIwaW50ZXJpZXVyfGVufDB8fHx8MTc1MTc0MTUyN3ww&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1613744627911-e7e34634fee3?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxwYXJrZXQlMkMlMjBob3V0ZW4lMjB2bG9lciUyQyUyMHJlbm92YXRpZXxlbnwwfHx8fDE3NTE3NDE1Mjd8MA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1675050794600-665b38f67c2a?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxwYXJrZXQlMjBvbmRlcmhvdWQlMkMlMjB2bG9lciUyMHNjaHVyZW4lMkMlMjB2YWttYW5zY2hhcHxlbnwwfHx8fDE3NTE3NDE1Mjd8MA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1505576391880-b3f9d713dc4f?ixlib=rb-4.1.0&w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1712038487428-7f7f5d76b69b?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxwYXJrZXR2bG9lciUyQyUyMHZpc2dyYWF0JTJDJTIwaW50ZXJpZXVyfGVufDB8fHx8MTc1MTc0MTUyN3ww&ixlib=rb-4.1.0?w=1024&h=1024"
							alt="Prachtig gelegde visgraat parketvloer"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Parketvloer Specialist Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Voor het leggen, schuren en onderhouden van uw houten vloer.
								Ambachtelijk vakmanschap voor een tijdloos resultaat.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een parketspecialist{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Parket Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Vakmanschap voor een houten vloer die generaties lang meegaat.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{services.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Zekerheid van Klusgebied
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Kies voor een specialist via Klusgebied en wees verzekerd van
								een prachtige en duurzame vloer.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-amber-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-amber-500 to-orange-600">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Klaar voor een prachtige houten vloer?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Plaats uw klus en ontvang vrijblijvend offertes van de beste
							parketspecialisten in uw regio.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-amber-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Plaats nu je parket klus
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_ParketvloerSpecialistPage;

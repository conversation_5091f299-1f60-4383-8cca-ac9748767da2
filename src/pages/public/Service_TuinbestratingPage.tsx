/**
 * @description This component renders a comprehensive and SEO-optimized detail page for garden paving services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for garden paving.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import { ArrowLeft, ArrowRight, Grip, Award, Sun } from "lucide-react";

const Service_TuinbestratingPage = () => {
	usePageTitle("Tuinbestrating | Klusgebied - Voor Terras & Oprit");
	const navigate = useNavigate();

	const services = [
		{
			title: "Terras Aanleggen",
			description:
				"Creëer een prachtig en functioneel terras met tegels, klinkers of natuursteen.",
		},
		{
			title: "Oprit Bestraten",
			description:
				"Een stevige en duurzame oprit die bestand is tegen zware belasting.",
		},
		{
			title: "Sierbestrating",
			description:
				"Decoratieve patronen en combinaties van materialen voor een unieke uitstraling.",
		},
		{
			title: "Keramische Tegels Leggen",
			description:
				"Luxe, krasvaste en onderhoudsvriendelijke keramische tegels voor buiten.",
		},
	];

	const benefits = [
		{
			icon: <Sun className="w-8 h-8 text-white" />,
			title: "Stijlvol Buitenleven",
			description:
				"Een mooi terras is het verlengstuk van uw woonkamer en verhoogt uw leefgenot.",
		},
		{
			icon: <Grip className="w-8 h-8 text-white" />,
			title: "Duurzaam & Sterk",
			description:
				"Wij zorgen voor een solide ondergrond zodat uw bestrating jarenlang perfect blijft liggen.",
		},
		{
			icon: <Award className="w-8 h-8 text-white" />,
			title: "Vakmanschap",
			description:
				"Onze stratenmakers hebben oog voor detail en zorgen voor een perfecte afwerking.",
		},
	];

	const faqs = [
		{
			question: "Wat kost tuinbestrating per m²?",
			answer:
				"De kosten variëren sterk, van €50 tot €150 per m², inclusief materiaal en arbeid. De prijs is afhankelijk van de gekozen tegel of steen en de complexiteit van het legverband.",
		},
		{
			question: "Welk type tegel is het beste voor mijn terras?",
			answer:
				"Keramische tegels zijn populair vanwege hun duurzaamheid en onderhoudsgemak. Betontegels zijn voordeliger en verkrijgbaar in vele stijlen. Natuursteen geeft een luxe, unieke uitstraling.",
		},
		{
			question: "Hoe voorkom ik onkruid tussen de tegels?",
			answer:
				"Een goede, waterdoorlatende voeg en een solide ondergrond zijn essentieel. Wij gebruiken speciale voegmiddelen die onkruidgroei tegengaan.",
		},
		{
			question: "Kan ik bestrating op een bestaand terras leggen?",
			answer:
				"Dit is soms mogelijk, maar meestal is het beter om de oude laag te verwijderen en een nieuwe, stabiele ondergrond van zand en eventueel puin aan te brengen voor het beste resultaat.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1724607809324-ffec13685c86?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxtb2Rlcm4lMjB0ZXJyYWNlJTJDJTIwb3V0ZG9vciUyMGZ1cm5pdHVyZSUyQyUyMGdhcmRlbiUyMGRlc2lnbnxlbnwwfHx8fDE3NTE3NDI0NzB8MA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1626228672398-940ff5fc15dd?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxwYXZlZCUyMGRyaXZld2F5JTJDJTIwbGFuZHNjYXBpbmclMkMlMjBvdXRkb29yJTIwc3BhY2V8ZW58MHx8fHwxNzUxNzQyNDcwfDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1684050532687-a54a3b18bfac?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxkZWNvcmF0aXZlJTIwcGF2aW5nJTJDJTIwZ2FyZGVuJTIwcGF0aCUyQyUyMHVuaXF1ZSUyMGRlc2lnbnxlbnwwfHx8fDE3NTE3NDI0Njl8MA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1697803586572-aaa47de7587d?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxjZXJhbWljJTIwdGlsZXMlMkMlMjBvdXRkb29yJTIwZmxvb3JpbmclMkMlMjBsdXh1cnklMjBwYXRpb3xlbnwwfHx8fDE3NTE3NDI0NzB8MA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1724607809324-ffec13685c86?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxtb2Rlcm4lMjB0ZXJyYWNlJTJDJTIwb3V0ZG9vciUyMGZ1cm5pdHVyZSUyQyUyMGdhcmRlbiUyMGRlc2lnbnxlbnwwfHx8fDE3NTE3NDI0NzB8MA&ixlib=rb-4.1.0?w=1024&h=1024"
							alt="Een modern aangelegd terras met tuinmeubilair"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Tuinbestrating Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Laat uw terras, oprit of tuinpad vakkundig aanleggen door een
								ervaren stratenmaker.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een stratenmaker{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Bestratingsdiensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Voor een duurzaam en prachtig resultaat dat past bij uw tuin.
							</p>
						</div>
						<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
							{services.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 p-8 rounded-2xl shadow-sm hover:shadow-xl hover:-translate-y-1 transition-all duration-300"
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600">{service.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Voordelen van Professionele Bestrating
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Geniet jarenlang van een strak en onderhoudsarm terras of oprit.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				<section className="bg-gradient-to-r from-green-500 to-teal-600">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Klaar om uw tuin te transformeren?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Vraag een vrijblijvende offerte aan en laat uw droomterras
							aanleggen door een vakman.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-green-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Vraag nu een offerte aan
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_TuinbestratingPage;

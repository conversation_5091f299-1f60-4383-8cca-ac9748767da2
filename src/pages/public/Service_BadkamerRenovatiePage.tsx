/**
 * @description This component renders a comprehensive and SEO-optimized detail page for bathroom renovation services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for bathroom renovations.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	ArrowRight,
	Bath,
	CheckSquare,
	Palette,
} from "lucide-react";

const Service_BadkamerRenovatiePage = () => {
	usePageTitle("Badkamer Renovatie | Klusgebied - Van A tot Z geregeld");
	const navigate = useNavigate();

	const services = [
		{
			title: "Totaalrenovatie",
			description:
				"Wij nemen de volledige renovatie van uw badkamer uit handen, van sloop tot oplevering.",
			longDescription:
				"Droomt u van een compleet nieuwe badkamer? Wij regelen alles. Van het slopen van de oude badkamer en het verleggen van leidingen tot het tegelwerk, de installatie van sanitair en de eindafwerking. Eén aanspreekpunt voor een zorgeloze renovatie.",
		},
		{
			title: "Tegelwerk",
			description:
				"Vakkundig plaatsen van wand- en vloertegels in elk gewenst patroon.",
			longDescription:
				"Tegels bepalen de sfeer van uw badkamer. Onze tegelzetters zijn meesters in hun vak en plaatsen alle soorten tegels, van grootformaat tot mozaïek, in elk gewenst patroon. Wij garanderen strak en waterdicht tegelwerk voor een luxe uitstraling.",
		},
		{
			title: "Sanitair Installatie",
			description:
				"Installatie van toiletten, wastafels, douches, baden en kranen.",
			longDescription:
				"Wij installeren al uw nieuwe sanitair met de grootste zorg. Of u nu kiest voor een inloopdouche, een vrijstaand bad of een modern hangtoilet, wij zorgen voor een vakkundige en waterdichte installatie, zodat u jarenlang kunt genieten van uw nieuwe badkamer.",
		},
		{
			title: "Leidingwerk Aanpassen",
			description:
				"Verleggen en aanpassen van waterleidingen en afvoeren voor een nieuwe indeling.",
			longDescription:
				"Wilt u de indeling van uw badkamer veranderen? Geen probleem. Onze loodgieters verleggen de waterleidingen en afvoeren naar de nieuwe posities. Dit doen we efficiënt en volgens de geldende normen, zodat alles perfect functioneert.",
		},
	];

	const benefits = [
		{
			icon: <CheckSquare className="w-8 h-8 text-white" />,
			title: "All-in-One Service",
			description:
				"Eén aanspreekpunt voor het gehele project, van ontwerp tot de laatste kitrand.",
		},
		{
			icon: <Palette className="w-8 h-8 text-white" />,
			title: "Design op Maat",
			description:
				"Wij helpen u met het ontwerpen van een badkamer die perfect past bij uw stijl en wensen.",
		},
		{
			icon: <Bath className="w-8 h-8 text-white" />,
			title: "Vaste Prijs & Planning",
			description:
				"Geen verrassingen achteraf. U ontvangt een duidelijke offerte en een realistische planning.",
		},
	];

	const faqs = [
		{
			question: "Wat kost een complete badkamerrenovatie?",
			answer:
				"Een gemiddelde badkamerrenovatie kost tussen de €7.500 en €15.000. De prijs is sterk afhankelijk van de grootte, materiaalkeuze en de complexiteit van het werk.",
		},
		{
			question: "Hoe lang duurt een badkamerrenovatie?",
			answer:
				"Gemiddeld duurt een volledige renovatie 2 tot 3 weken. Dit is inclusief sloop, leidingwerk, tegelen, installatie en afwerking.",
		},
		{
			question: "Kan ik tijdens de verbouwing thuis blijven wonen?",
			answer:
				"Ja, dat is meestal mogelijk. Houd er wel rekening mee dat u tijdelijk geen gebruik kunt maken van de badkamer en er enige overlast van stof en geluid kan zijn.",
		},
		{
			question: "Helpen jullie ook met het ontwerp van de badkamer?",
			answer:
				"Zeker! Onze specialisten denken graag met u mee over een praktische en stijlvolle indeling en helpen u bij het kiezen van de juiste materialen en sanitair.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1651442897558-47cff0f64bd9?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxtb2Rlcm4lMjBiYXRocm9vbSUyQyUyMGx1eHVyeSUyMGRlc2lnbiUyQyUyMGJhdGhyb29tJTIwcmVub3ZhdGlvbnxlbnwwfHx8fDE3NTE3NDIzNDd8MA&ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1696815115263-862b21c899b7?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxiYXRocm9vbSUyMHRpbGVzJTJDJTIwZWxlZ2FudCUyMGludGVyaW9yJTJDJTIwaG9tZSUyMHJlbm92YXRpb258ZW58MHx8fHwxNzUxNzQyMzQ3fDA&ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1629969337555-e384ed2d1439?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxzYW5pdGFyeSUyMGluc3RhbGxhdGlvbiUyQyUyMHN0eWxpc2glMjBiYXRocm9vbSUyQyUyMG1vZGVybiUyMGZpeHR1cmVzfGVufDB8fHx8MTc1MTc0MjM4MXww&ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1600585152220-90363fe7e115?ixlib=rb-4.1.0&w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1651442897558-47cff0f64bd9?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxtb2Rlcm4lMjBiYXRocm9vbSUyQyUyMGx1eHVyeSUyMGRlc2lnbiUyQyUyMGJhdGhyb29tJTIwcmVub3ZhdGlvbnxlbnwwfHx8fDE3NTE3NDIzNDd8MA&ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Moderne, luxe badkamer"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/60"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Uw Droombadkamer Gerealiseerd
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Van ontwerp tot oplevering, wij transformeren uw oude badkamer
								in een oase van rust en comfort.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Start uw badkamerrenovatie{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Badkamer Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Een compleet pakket voor een zorgeloze renovatie.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{services.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Zekerheid van Klusgebied
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Kies voor een ervaren team en geniet van een vakkundig
								gerenoveerde badkamer.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-teal-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				<section className="bg-gradient-to-r from-teal-500 to-cyan-500">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Klaar voor de badkamer van uw dromen?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Vraag een vrijblijvende offerte aan en ontdek de mogelijkheden
							voor uw badkamer.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-teal-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Vraag nu een offerte aan
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_BadkamerRenovatiePage;

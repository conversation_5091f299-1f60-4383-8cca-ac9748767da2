/**
 * @description This component renders a comprehensive FAQ page for the Klusgebied platform. It features a dynamic hero section, a detailed and interactive FAQ accordion, and a final call-to-action to drive user engagement. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include the faqs array which contains all the question and answer data, and the imported components like Header, Footer, FAQSection, and FinalCTA that structure the page.
 */
import React from "react";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import FAQSection from "../../components/landing/FAQSection";
import FinalCTA from "../../components/landing/FinalCTA";
import usePageTitle from "../../hooks/usePageTitle";
import { HelpCircle, Search, Wrench } from "lucide-react";

const FAQPage = () => {
	usePageTitle("Veelgestelde Vragen | Klusgebied");

	const faqs = [
		{
			question: "Hoe plaats ik een klus op Klusgebied?",
			answer:
				'Een klus plaatsen is eenvoudig en gratis. Klik op de knop "Plaats een klus", omschrijf wat er moet gebeuren, voeg eventueel foto\'s toe en geef aan wanneer je de klus wilt laten uitvoeren. Binnen enkele uren ontvang je reacties van geïnteresseerde vakmannen.',
		},
		{
			question: "Zijn de vakmannen op Klusgebied betrouwbaar?",
			answer:
				"Ja, absoluut. Wij verifiëren elke vakman die zich aanmeldt. We controleren hun KVK-inschrijving, verzekeringen en eventuele certificaten. Daarnaast kun je reviews van eerdere klanten lezen om een goed beeld te krijgen van hun werk.",
		},
		{
			question: "Wat kost het om een klus te plaatsen?",
			answer:
				"Het plaatsen van een klus is volledig gratis en vrijblijvend. Je betaalt pas als je een vakman hebt gekozen en de klus is uitgevoerd. De betaling regel je direct met de vakman.",
		},
		{
			question: "Hoe kies ik de beste vakman voor mijn klus?",
			answer:
				"Vergelijk de offertes die je ontvangt op basis van prijs, voorgestelde aanpak en de reviews van de vakman. Je kunt ook via ons platform chatten met de vakmannen om vragen te stellen voordat je een definitieve keuze maakt.",
		},
		{
			question: "Wat als ik niet tevreden ben met het resultaat?",
			answer:
				"Wij streven naar 100% tevredenheid. Mocht je onverhoopt niet tevreden zijn, neem dan eerst contact op met de vakman om samen een oplossing te zoeken. Komen jullie er niet uit? Dan kan onze klantenservice bemiddelen. Veel klussen komen ook met een garantie.",
		},
		{
			question: "Hoe werkt de betaling?",
			answer:
				"De betaling spreek je rechtstreeks af met de gekozen vakman. De meeste vakmannen sturen een factuur na afronding van de klus. We adviseren om de betalingsvoorwaarden altijd vooraf duidelijk af te spreken.",
		},
		{
			question: "Kan ik ook een spoedklus plaatsen?",
			answer:
				"Jazeker. Geef bij het plaatsen van je klus aan dat het om een spoedgeval gaat. We hebben een netwerk van vakmannen die vaak snel beschikbaar zijn voor urgente zaken zoals een lekkage of stroomstoring.",
		},
		{
			question: "Welke diensten biedt Klusgebied aan?",
			answer:
				"Klusgebied biedt een breed scala aan diensten, van loodgieters en elektriciens tot schilders, timmerlieden en tuinmannen. Je kunt bij ons terecht voor vrijwel elke klus in en om het huis, van kleine reparaties tot grote renovaties.",
		},
	];

	return (
		<div className="bg-slate-50">
			<Header />
			<main>
				<div className="relative bg-white pt-24 sm:pt-32 lg:pt-40">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
						<div className="inline-flex items-center justify-center bg-teal-100 text-teal-600 rounded-full p-3 mb-6 motion-preset-fade-down">
							<HelpCircle className="w-8 h-8" />
						</div>
						<h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-900 tracking-tight motion-preset-fade-down motion-delay-100">
							Veelgestelde Vragen
						</h1>
						<p className="mt-6 max-w-3xl mx-auto text-lg md:text-xl text-slate-600 motion-preset-fade-down motion-delay-200">
							Heb je een vraag over hoe Klusgebied werkt? Hier vind je de
							antwoorden op de meest gestelde vragen. Staat je vraag er niet
							tussen? Neem dan gerust contact met ons op.
						</p>
					</div>
				</div>

				<FAQSection faqs={faqs} />

				<FinalCTA />
			</main>
			<Footer />
		</div>
	);
};

export default FAQPage;

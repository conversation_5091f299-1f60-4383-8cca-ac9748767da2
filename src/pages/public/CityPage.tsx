/**
 * @description This component renders city-specific landing pages with localized service information and professional listings.
 * It dynamically displays content based on the city parameter from the URL, showing local services, statistics, and call-to-action elements.
 * The component includes SEO-optimized content, responsive design, and animated elements for enhanced user engagement with city-specific targeting.
 * Key variables include cityName from URL params, currentCity object with city data, and localized service information for improved geographic targeting.
 */

import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
	ArrowLeft,
	MapPin,
	Star,
	Users,
	CheckCircle,
	Phone,
	Award,
	TrendingUp,
} from "lucide-react";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import usePageTitle from "../../hooks/usePageTitle";

const CityPage = () => {
	const { cityName } = useParams();
	const navigate = useNavigate();
	const [selectedService, setSelectedService] = useState(null);

	// City data with comprehensive information
	const cityData = {
		// Noord-Holland
		amsterdam: {
			name: "Amsterdam",
			province: "Noord-Holland",
			population: "872.000",
			professionals: 380,
			completedJobs: 4200,
			rating: 4.8,
			image:
				"https://images.unsplash.com/photo-1534351590666-13e3e96b5017?ixlib=rb-4.1.0&w=1024&h=1024",
			description:
				"De hoofdstad van Nederland met haar iconische grachten en rijke geschiedenis biedt uitdagingen voor elke vakman. Van monumentale panden tot moderne nieuwbouw.",
			neighborhoods: [
				"Centrum",
				"Jordaan",
				"Oud-Zuid",
				"Noord",
				"Oost",
				"West",
				"Zuidoost",
				"Nieuw-West",
			],
		},
		haarlem: {
			name: "Haarlem",
			province: "Noord-Holland",
			population: "162.000",
			professionals: 150,
			completedJobs: 1800,
			rating: 4.7,
			image: "https://heyboss.heeyo.ai/1751506432-9bf05a21.webp",
			description:
				"De charmante hoofdstad van Noord-Holland, bekend om zijn hofjes en historische sfeer.",
			neighborhoods: [
				"Centrum",
				"Schalkwijk",
				"Haarlem-Noord",
				"Zuid-West",
				"Oost",
			],
		},
		alkmaar: {
			name: "Alkmaar",
			province: "Noord-Holland",
			population: "109.000",
			professionals: 110,
			completedJobs: 1200,
			rating: 4.6,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp",
			description:
				"Beroemd om zijn kaasmarkt, is Alkmaar een stad met een rijke historie en een levendig centrum.",
			neighborhoods: ["Centrum", "Oudorp", "De Mare", "Zuid", "West"],
		},
		hilversum: {
			name: "Hilversum",
			province: "Noord-Holland",
			population: "90.000",
			professionals: 95,
			completedJobs: 1000,
			rating: 4.7,
			image: "https://heyboss.heeyo.ai/1751506435-072e5d66.webp",
			description:
				"De mediastad van Nederland, omgeven door prachtige natuur en architectuur.",
			neighborhoods: ["Centrum", "Zuid", "Oost", "Noordwest", "Kerkelanden"],
		},
		amstelveen: {
			name: "Amstelveen",
			province: "Noord-Holland",
			population: "92.000",
			professionals: 100,
			completedJobs: 1100,
			rating: 4.8,
			image:
				"https://images.unsplash.com/photo-1694002478150-510243dbce34?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxBbXN0ZWx2ZWVuJTJDJTIwbW9kZXJuJTIwY2l0eSUyQyUyMGdyZWVuJTIwc3BhY2V8ZW58MHx8fHwxNzUxNTA2NDMwfDA&ixlib=rb-4.1.0?w=1024&h=1024",
			description:
				"Een groene en moderne stad onder de rook van Amsterdam, met een internationaal karakter.",
			neighborhoods: ["Stadshart", "Randwijck", "Westwijk", "Groenelaan"],
		},
		hoofddorp: {
			name: "Hoofddorp",
			province: "Noord-Holland",
			population: "77.000",
			professionals: 80,
			completedJobs: 900,
			rating: 4.5,
			image:
				"https://images.unsplash.com/photo-1561489396-888724a1543d?ixlib=rb-4.1.0&w=1024&h=1024",
			description:
				"Het bruisende centrum van de Haarlemmermeer, een jonge stad met veel groeipotentieel.",
			neighborhoods: ["Centrum", "Floriande", "Graan voor Visch", "Toolenburg"],
		},
		zaandam: {
			name: "Zaandam",
			province: "Noord-Holland",
			population: "78.000",
			professionals: 85,
			completedJobs: 950,
			rating: 4.6,
			image: "https://heyboss.heeyo.ai/1751506435-a1a5ca77.webp",
			description:
				"Bekend om zijn unieke architectuur en industriële verleden aan de Zaan.",
			neighborhoods: ["Centrum", "Westerwatering", "Poelenburg", "Peldersveld"],
		},
		purmerend: {
			name: "Purmerend",
			province: "Noord-Holland",
			population: "81.000",
			professionals: 90,
			completedJobs: 980,
			rating: 4.5,
			image:
				"https://images.unsplash.com/photo-1652143180997-b74cadf6514c?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxQdXJtZXJlbmQlMkMlMjBtYXJrZXQlMjB0b3duJTJDJTIwd2F0ZXJ8ZW58MHx8fHwxNzUxNTA2NDMwfDA&ixlib=rb-4.1.0?w=1024&h=1024",
			description:
				"De marktstad van Laag-Holland, centraal gelegen tussen polders en water.",
			neighborhoods: ["Centrum", "Weidevenne", "Gors", "Overwhere"],
		},
		hoorn: {
			name: "Hoorn",
			province: "Noord-Holland",
			population: "73.000",
			professionals: 75,
			completedJobs: 850,
			rating: 4.7,
			image:
				"https://images.unsplash.com/photo-1657406894638-477fdc52df5e?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxIb29ybiUyQyUyMGhpc3RvcmljYWwlMjBjaXR5JTJDJTIwVk9DfGVufDB8fHx8MTc1MTUwNjQzMHww&ixlib=rb-4.1.0?w=1024&h=1024",
			description:
				"Een historische VOC-stad aan het Markermeer met een prachtig bewaard gebleven binnenstad.",
			neighborhoods: ["Binnenstad", "Kersenboogerd", "Risdam", "Grote Waal"],
		},
		"den-helder": {
			name: "Den Helder",
			province: "Noord-Holland",
			population: "56.000",
			professionals: 60,
			completedJobs: 700,
			rating: 4.4,
			image:
				"https://images.unsplash.com/photo-1699154581431-3ec6bb850773?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxEZW4lMjBIZWxkZXIlMkMlMjBtYXJpbmUlMjBjaXR5JTJDJTIwY29hc3RhbHxlbnwwfHx8fDE3NTE1MDY0MzB8MA&ixlib=rb-4.1.0?w=1024&h=1024",
			description:
				"De marinehaven van Nederland, een stoere stad omringd door zee en duinen.",
			neighborhoods: [
				"Centrum",
				"Nieuw-Den Helder",
				"De Schooten",
				"Julianadorp",
			],
		},

		// Zuid-Holland
		rotterdam: {
			name: "Rotterdam",
			province: "Zuid-Holland",
			population: "651.000",
			professionals: 295,
			completedJobs: 3100,
			rating: 4.7,
			image:
				"https://images.unsplash.com/photo-1663000921466-951ee5aa20a8?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxSb3R0ZXJkYW0lMkMlMjBtb2Rlcm4lMjBhcmNoaXRlY3R1cmUlMkMlMjBoYXJib3J8ZW58MHx8fHwxNzUxNTAzNzU5fDA&ixlib=rb-4.1.0?w=1024&h=1024",
			description:
				"De moderne havenstad van Nederland, waar innovatieve architectuur en industriële heritage samenkomen. Elke wijk heeft zijn eigen karakter en klusbehoeften.",
			neighborhoods: [
				"Centrum",
				"Delfshaven",
				"Noord",
				"Kralingen-Crooswijk",
				"Prins Alexander",
				"Charlois",
				"IJsselmonde",
				"Pernis",
			],
		},
		"den-haag": {
			name: "Den Haag",
			province: "Zuid-Holland",
			population: "548.000",
			professionals: 260,
			completedJobs: 2950,
			rating: 4.6,
			image:
				"https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?ixlib=rb-4.1.0&w=1024&h=1024",
			description:
				"De internationale stad van vrede en recht, met een unieke mix van regeringsgebouwen, ambassades en karakteristieke woonwijken aan zee.",
			neighborhoods: [
				"Centrum",
				"Benoordenhout",
				"Voorburg",
				"Leidschenveen",
				"Ypenburg",
				"Scheveningen",
				"Laak",
				"Escamp",
			],
		},
		leiden: {
			name: "Leiden",
			province: "Zuid-Holland",
			population: "125.000",
			professionals: 130,
			completedJobs: 1500,
			rating: 4.8,
			image:
				"https://images.unsplash.com/photo-1721846271676-a994b6eb8615?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxMZWlkZW4lMkMlMjB1bml2ZXJzaXR5JTIwY2l0eSUyQyUyMGhpc3RvcmljYWx8ZW58MHx8fHwxNzUxNTA2NDMwfDA&ixlib=rb-4.1.0?w=1024&h=1024",
			description:
				"De stad van ontdekkingen, met de oudste universiteit van Nederland en een sfeervolle, historische binnenstad.",
			neighborhoods: [
				"Binnenstad",
				"Stevenshof",
				"Merenwijk",
				"Zuidwest",
				"Leiden Noord",
			],
		},
		dordrecht: {
			name: "Dordrecht",
			province: "Zuid-Holland",
			population: "119.000",
			professionals: 120,
			completedJobs: 1400,
			rating: 4.6,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp0",
			description:
				"De oudste stad van Holland, prachtig gelegen op een eiland omringd door rivieren.",
			neighborhoods: ["Centrum", "Stadspolders", "Sterrenburg", "Dubbeldam"],
		},
		zoetermeer: {
			name: "Zoetermeer",
			province: "Zuid-Holland",
			population: "125.000",
			professionals: 125,
			completedJobs: 1450,
			rating: 4.5,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				'Een moderne stad met veel groen en uitstekende voorzieningen, bekend als "Sweet Lake City".',
			neighborhoods: ["Stadshart", "Noordhove", "Rokkeveen", "Oosterheem"],
		},
		delft: {
			name: "Delft",
			province: "Zuid-Holland",
			population: "103.000",
			professionals: 115,
			completedJobs: 1300,
			rating: 4.9,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"De stad van Delfts Blauw, Oranje en Vermeer, met een rijke geschiedenis en een innovatief technisch hart.",
			neighborhoods: ["Binnenstad", "Tanthof", "Vrijenban", "Hof van Delft"],
		},
		gouda: {
			name: "Gouda",
			province: "Zuid-Holland",
			population: "73.000",
			professionals: 80,
			completedJobs: 900,
			rating: 4.6,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp3",
			description:
				"Wereldberoemd om zijn kaas en stroopwafels, een historische stad met een prachtig stadhuis.",
			neighborhoods: [
				"Binnenstad",
				"Korte Akkeren",
				"Goverwelle",
				"Bloemendaal",
			],
		},
		"alphen-aan-den-rijn": {
			name: "Alphen aan den Rijn",
			province: "Zuid-Holland",
			population: "112.000",
			professionals: 110,
			completedJobs: 1250,
			rating: 4.5,
			image:
				"https://images.unsplash.com/photo-1652143180997-b74cadf6514c?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxQdXJtZXJlbmQlMkMlMjBtYXJrZXQlMjB0b3duJTJDJTIwd2F0ZXJ8ZW58MHx8fHwxNzUxNTA2NDMwfDA&ixlib=rb-4.1.0?w=1024&h=1024",
			description:
				"Centraal gelegen in het Groene Hart, een stad die water en groen combineert.",
			neighborhoods: ["Centrum", "Ridderveld", "Kerk en Zanen", "Hoge Zijde"],
		},
		spijkenisse: {
			name: "Spijkenisse",
			province: "Zuid-Holland",
			population: "72.000",
			professionals: 70,
			completedJobs: 800,
			rating: 4.4,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"Een groeistad op Voorne-Putten met moderne architectuur en veel voorzieningen.",
			neighborhoods: ["Centrum", "De Akkers", "Groenewoud", "Waterland"],
		},
		katwijk: {
			name: "Katwijk",
			province: "Zuid-Holland",
			population: "66.000",
			professionals: 75,
			completedJobs: 850,
			rating: 4.7,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp6",
			description:
				"Een levendige badplaats met een sterke visserijcultuur en een prachtig strand.",
			neighborhoods: [
				"Katwijk aan Zee",
				"Katwijk aan den Rijn",
				"Rijnsburg",
				"Valkenburg",
			],
		},

		// Utrecht
		utrecht: {
			name: "Utrecht",
			province: "Utrecht",
			population: "361.000",
			professionals: 220,
			completedJobs: 2800,
			rating: 4.9,
			image:
				"https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.1.0&w=1024&h=1024",
			description:
				"Het kloppende hart van Nederland, waar historie en moderniteit hand in hand gaan. Van middeleeuwse binnenstad tot bloeiende nieuwbouwwijken.",
			neighborhoods: [
				"Binnenstad",
				"Noordoost",
				"Noordwest",
				"Oost",
				"Zuid",
				"West",
				"Zuidwest",
				"Leidsche Rijn",
			],
		},
		amersfoort: {
			name: "Amersfoort",
			province: "Utrecht",
			population: "157.000",
			professionals: 160,
			completedJobs: 1900,
			rating: 4.8,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp7",
			description:
				"Een keistad met een middeleeuws centrum en moderne architectuur, vol leven en historie.",
			neighborhoods: ["Binnenstad", "Kattenbroek", "Vathorst", "Schothorst"],
		},
		veenendaal: {
			name: "Veenendaal",
			province: "Utrecht",
			population: "66.000",
			professionals: 70,
			completedJobs: 800,
			rating: 4.6,
			image:
				"https://images.unsplash.com/photo-1652143180997-b74cadf6514c?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxQdXJtZXJlbmQlMkMlMjBtYXJrZXQlMjB0b3duJTJDJTIwd2F0ZXJ8ZW58MHx8fHwxNzUxNTA2NDMwfDA&ixlib=rb-4.1.0?w=1024&h=1024",
			description:
				"Centraal gelegen op de Utrechtse Heuvelrug, een actieve stad met een groot winkelhart.",
			neighborhoods: ["Centrum", "West", "Oost", "Zuid"],
		},
		nieuwegein: {
			name: "Nieuwegein",
			province: "Utrecht",
			population: "63.000",
			professionals: 65,
			completedJobs: 750,
			rating: 4.5,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"Een jonge, dynamische stad met een modern stadshart en uitstekende verbindingen.",
			neighborhoods: ["Batau", "Doorslag", "Galecop", "Stadscentrum"],
		},
		zeist: {
			name: "Zeist",
			province: "Utrecht",
			population: "65.000",
			professionals: 70,
			completedJobs: 820,
			rating: 4.7,
			image:
				"https://images.unsplash.com/photo-1652143180997-b74cadf6514c?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxQdXJtZXJlbmQlMkMlMjBtYXJrZXQlMjB0b3duJTJDJTIwd2F0ZXJ8ZW58MHx8fHwxNzUxNTA2NDMwfDA&ixlib=rb-4.1.0?w=1024&h=1024",
			description:
				"Een groene gemeente met statige lanen en het prachtige Slot Zeist.",
			neighborhoods: ["Centrum", "West", "Noord", "Hoge Dennen"],
		},
		woerden: {
			name: "Woerden",
			province: "Utrecht",
			population: "52.000",
			professionals: 55,
			completedJobs: 650,
			rating: 4.6,
			image:
				"https://images.unsplash.com/photo-1652143180997-b74cadf6514c?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxQdXJtZXJlbmQlMkMlMjBtYXJrZXQlMjB0b3duJTJDJTIwd2F0ZXJ8ZW58MHx8fHwxNzUxNTA2NDMwfDA&ixlib=rb-4.1.0?w=1024&h=1024",
			description:
				"Een historische vestingstad in het Groene Hart, bekend om zijn kaasmarkt.",
			neighborhoods: [
				"Binnenstad",
				"Snel en Polanen",
				"Molenvliet",
				"Schilderskwartier",
			],
		},
		ijsselstein: {
			name: "IJsselstein",
			province: "Utrecht",
			population: "34.000",
			professionals: 40,
			completedJobs: 500,
			rating: 4.7,
			image:
				"https://images.unsplash.com/photo-1652143180997-b74cadf6514c?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxQdXJtZXJlbmQlMkMlMjBtYXJrZXQlMjB0b3duJTJDJTIwd2F0ZXJ8ZW58MHx8fHwxNzUxNTA2NDMwfDA&ixlib=rb-4.1.0?w=1024&h=1024",
			description:
				"Bekend om de Gerbrandytoren, een historische stad met een moderne uitstraling.",
			neighborhoods: ["Binnenstad", "Zenderpark", "Nieuwpoort", "Achtersloot"],
		},
		houten: {
			name: "Houten",
			province: "Utrecht",
			population: "50.000",
			professionals: 55,
			completedJobs: 680,
			rating: 4.8,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"Meerdere malen uitgeroepen tot Fietsstad van Nederland, een moderne en groene gemeente.",
			neighborhoods: ["Houten-Zuid", "Houten-Noord", "Centrum", "De Lobben"],
		},
		"de-bilt": {
			name: "De Bilt",
			province: "Utrecht",
			population: "43.000",
			professionals: 45,
			completedJobs: 550,
			rating: 4.7,
			image:
				"https://images.unsplash.com/photo-1652143180997-b74cadf6514c?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxQdXJtZXJlbmQlMkMlMjBtYXJrZXQlMjB0b3duJTJDJTIwd2F0ZXJ8ZW58MHx8fHwxNzUxNTA2NDMwfDA&ixlib=rb-4.1.0?w=1024&h=1024",
			description:
				"De thuisbasis van het KNMI, een groene gemeente met een rijke historie.",
			neighborhoods: ["De Bilt", "Bilthoven", "Maartensdijk", "Groenekan"],
		},
		bunnik: {
			name: "Bunnik",
			province: "Utrecht",
			population: "15.000",
			professionals: 20,
			completedJobs: 250,
			rating: 4.6,
			image:
				"https://images.unsplash.com/photo-1652143180997-b74cadf6514c?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxQdXJtZXJlbmQlMkMlMjBtYXJrZXQlMjB0b3duJTJDJTIwd2F0ZXJ8ZW58MHx8fHwxNzUxNTA2NDMwfDA&ixlib=rb-4.1.0?w=1024&h=1024",
			description:
				"Een landelijke gemeente aan de Kromme Rijn, met een rijke historie en natuur.",
			neighborhoods: ["Bunnik", "Odijk", "Werkhoven"],
		},

		// Gelderland
		arnhem: {
			name: "Arnhem",
			province: "Gelderland",
			population: "160.000",
			professionals: 155,
			completedJobs: 1850,
			rating: 4.6,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp7",
			description:
				"Stad van mode en design, gelegen aan de Rijn en omringd door de Veluwe.",
			neighborhoods: ["Centrum", "Spijkerkwartier", "Klarendal", "Schuytgraaf"],
		},
		nijmegen: {
			name: "Nijmegen",
			province: "Gelderland",
			population: "177.000",
			professionals: 180,
			completedJobs: 2100,
			rating: 4.7,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp7",
			description:
				"De oudste stad van Nederland, vol historie, studentenleven en gelegen aan de Waal.",
			neighborhoods: ["Centrum", "Oost", "Dukenburg", "Lindenholt"],
		},
		apeldoorn: {
			name: "Apeldoorn",
			province: "Gelderland",
			population: "164.000",
			professionals: 160,
			completedJobs: 1900,
			rating: 4.6,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp7",
			description:
				"Koninklijke stad in het hart van de Veluwe, bekend om Paleis Het Loo en de Apenheul.",
			neighborhoods: ["Centrum", "De Maten", "Zuidbroek", "Osseveld"],
		},
		ede: {
			name: "Ede",
			province: "Gelderland",
			population: "117.000",
			professionals: 115,
			completedJobs: 1350,
			rating: 4.5,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp7",
			description:
				"Het centrum van de Foodvalley, gelegen op de grens van de Veluwe en de Gelderse Vallei.",
			neighborhoods: ["Centrum", "Veldhuizen", "Kernhem", "Bennekom"],
		},
		zutphen: {
			name: "Zutphen",
			province: "Gelderland",
			population: "48.000",
			professionals: 50,
			completedJobs: 600,
			rating: 4.7,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp7",
			description:
				"Een prachtige Hanzestad aan de IJssel met een van de best bewaarde middeleeuwse stadskernen.",
			neighborhoods: ["Centrum", "Leesten", "Waterkwartier", "Warnsveld"],
		},
		doetinchem: {
			name: "Doetinchem",
			province: "Gelderland",
			population: "58.000",
			professionals: 60,
			completedJobs: 700,
			rating: 4.5,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp7",
			description:
				"Het bruisende hart van de Achterhoek, een stad met een rijke historie en een levendig centrum.",
			neighborhoods: ["Centrum", "De Huet", "Wijnbergen", "Overstegen"],
		},
		tiel: {
			name: "Tiel",
			province: "Gelderland",
			population: "42.000",
			professionals: 45,
			completedJobs: 550,
			rating: 4.4,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp7",
			description:
				"De fruitstad van de Betuwe, bekend om Flipje en de Appelpop.",
			neighborhoods: ["Centrum", "Passewaaij", "Rauwenhof", "Drumpt"],
		},
		wageningen: {
			name: "Wageningen",
			province: "Gelderland",
			population: "40.000",
			professionals: 45,
			completedJobs: 580,
			rating: 4.8,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp7",
			description:
				"Internationale stad van de levenswetenschappen, met een wereldberoemde universiteit.",
			neighborhoods: ["Centrum", "Noordwest", "De Nude", "Kortenoord"],
		},
		harderwijk: {
			name: "Harderwijk",
			province: "Gelderland",
			population: "48.000",
			professionals: 50,
			completedJobs: 620,
			rating: 4.6,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp7",
			description:
				"Een historische Hanzestad aan het Veluwemeer, bekend om het Dolfinarium.",
			neighborhoods: ["Binnenstad", "Stadsweiden", "Drielanden", "Frankrijk"],
		},
		barneveld: {
			name: "Barneveld",
			province: "Gelderland",
			population: "59.000",
			professionals: 60,
			completedJobs: 720,
			rating: 4.5,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp7",
			description:
				"Het centrum van de pluimveesector in Nederland, een dorp met stadse allures.",
			neighborhoods: ["Centrum", "De Vaarst", "Oldenbarneveld", "Norschoten"],
		},

		// Noord-Brabant
		eindhoven: {
			name: "Eindhoven",
			province: "Noord-Brabant",
			population: "235.000",
			professionals: 240,
			completedJobs: 2800,
			rating: 4.7,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"De design- en technologiehoofdstad van Nederland, een stad vol innovatie en creativiteit.",
			neighborhoods: ["Centrum", "Strijp-S", "Woensel", "Gestel"],
		},
		tilburg: {
			name: "Tilburg",
			province: "Noord-Brabant",
			population: "220.000",
			professionals: 210,
			completedJobs: 2500,
			rating: 4.6,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"Een levendige studentenstad met een rijk textielverleden en een bruisend cultureel leven.",
			neighborhoods: ["Centrum", "Oud-Zuid", "Reeshof", "Oud-Noord"],
		},
		breda: {
			name: "Breda",
			province: "Noord-Brabant",
			population: "184.000",
			professionals: 190,
			completedJobs: 2200,
			rating: 4.8,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"De Parel van het Zuiden, een historische Nassaustad met een chique uitstraling.",
			neighborhoods: ["Centrum", "Ginneken", "Haagse Beemden", "Princenhage"],
		},
		"den-bosch": {
			name: "Den Bosch",
			province: "Noord-Brabant",
			population: "155.000",
			professionals: 160,
			completedJobs: 1900,
			rating: 4.7,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"De bourgondische hoofdstad van Brabant, bekend om de Sint-Jan en de Bossche Bollen.",
			neighborhoods: ["Binnenstad", "De Groote Wielen", "Maaspoort", "Zuid"],
		},
		helmond: {
			name: "Helmond",
			province: "Noord-Brabant",
			population: "92.000",
			professionals: 95,
			completedJobs: 1100,
			rating: 4.5,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"Een stad met een rijk industrieel verleden, nu een moderne stad met veel groen.",
			neighborhoods: ["Centrum", "Brouwhuis", "Rijpelberg", "Stiphout"],
		},
		roosendaal: {
			name: "Roosendaal",
			province: "Noord-Brabant",
			population: "77.000",
			professionals: 80,
			completedJobs: 950,
			rating: 4.4,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"Een stad op het snijvlak van Brabant en Zeeland, met een groot winkelcentrum.",
			neighborhoods: ["Centrum", "Kortendijk", "Tolberg", "Westrand"],
		},
		"bergen-op-zoom": {
			name: "Bergen op Zoom",
			province: "Noord-Brabant",
			population: "67.000",
			professionals: 70,
			completedJobs: 850,
			rating: 4.6,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"Een historische vestingstad met een prachtig stadspaleis en een levendige carnavalstraditie.",
			neighborhoods: ["Centrum", "Gageldonk", "Warande", "Meilust"],
		},
		oss: {
			name: "Oss",
			province: "Noord-Brabant",
			population: "92.000",
			professionals: 90,
			completedJobs: 1050,
			rating: 4.4,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"Een stad met een rijke industriële geschiedenis, nu een moderne stad met veel werkgelegenheid.",
			neighborhoods: ["Centrum", "Ussen", "Ruwaard", "Krabbenhof"],
		},
		oosterhout: {
			name: "Oosterhout",
			province: "Noord-Brabant",
			population: "56.000",
			professionals: 60,
			completedJobs: 700,
			rating: 4.5,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				'Een stad met een groen karakter, bekend om zijn "slotjes" en gezellige centrum.',
			neighborhoods: ["Centrum", "Dommelbergen", "Oosterheide", "Vrachelen"],
		},
		uden: {
			name: "Uden",
			province: "Noord-Brabant",
			population: "42.000",
			professionals: 45,
			completedJobs: 550,
			rating: 4.5,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"Een levendige plaats in de Peel, met een groot winkelgebied en veel evenementen.",
			neighborhoods: ["Centrum", "Zoggel", "Hoeven", "Bitswijk"],
		},

		// Overijssel
		enschede: {
			name: "Enschede",
			province: "Overijssel",
			population: "160.000",
			professionals: 150,
			completedJobs: 1800,
			rating: 4.6,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"De grootste stad van Twente, een bruisende studentenstad met een innovatief karakter.",
			neighborhoods: ["Centrum", "Roombeek", "Stroinkslanden", "Wesselerbrink"],
		},
		zwolle: {
			name: "Zwolle",
			province: "Overijssel",
			population: "130.000",
			professionals: 135,
			completedJobs: 1600,
			rating: 4.7,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"Een prachtige Hanzestad met een stervormige stadsgracht en een levendig cultureel leven.",
			neighborhoods: ["Binnenstad", "Assendorp", "Stadshagen", "Aa-landen"],
		},
		deventer: {
			name: "Deventer",
			province: "Overijssel",
			population: "101.000",
			professionals: 105,
			completedJobs: 1250,
			rating: 4.8,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"Een van de oudste steden van Nederland, bekend om zijn boekenmarkt en Dickens Festijn.",
			neighborhoods: ["Binnenstad", "Colmschate", "Zandweerd", "De Vijfhoek"],
		},
		almelo: {
			name: "Almelo",
			province: "Overijssel",
			population: "73.000",
			professionals: 75,
			completedJobs: 900,
			rating: 4.4,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"Een stad met een rijk textielverleden, nu een moderne stad met veel water en groen.",
			neighborhoods: [
				"Centrum",
				"Windmolenbroek",
				"Schelfhorst",
				"Sluitersveld",
			],
		},
		hengelo: {
			name: "Hengelo",
			province: "Overijssel",
			population: "81.000",
			professionals: 85,
			completedJobs: 1000,
			rating: 4.5,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"De metaalstad van Twente, een stad vol techniek en innovatie.",
			neighborhoods: ["Centrum", "Hasseler Es", "Slangenbeek", "Groot Driene"],
		},
		kampen: {
			name: "Kampen",
			province: "Overijssel",
			population: "54.000",
			professionals: 55,
			completedJobs: 650,
			rating: 4.7,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"Een prachtige Hanzestad aan de IJssel met een van de best bewaarde historische stadskernen.",
			neighborhoods: ["Binnenstad", "Brunnepe", "Flevowijk", "Hanzewijk"],
		},
		hardenberg: {
			name: "Hardenberg",
			province: "Overijssel",
			population: "61.000",
			professionals: 60,
			completedJobs: 700,
			rating: 4.5,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"Een stad in het Vechtdal, bekend om zijn gastvrijheid en evenementen.",
			neighborhoods: ["Centrum", "Baalder", "Marslanden", "Heemse"],
		},
		emmen: {
			name: "Emmen",
			province: "Drenthe",
			population: "107.000",
			professionals: 100,
			completedJobs: 1200,
			rating: 4.5,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"De grootste stad van Drenthe, bekend om Wildlands Adventure Zoo en het Atlas Theater.",
			neighborhoods: [
				"Centrum",
				"Emmer-Compascuum",
				"Klazienaveen",
				"Bargeres",
			],
		},
		oldenzaal: {
			name: "Oldenzaal",
			province: "Overijssel",
			population: "32.000",
			professionals: 35,
			completedJobs: 400,
			rating: 4.6,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"De Glimlach van Twente, een stad met een rijke historie en een bruisend carnavalsleven.",
			neighborhoods: ["Centrum", "Zuid-Berghuizen", "De Thij", "Graven Es"],
		},
		steenwijk: {
			name: "Steenwijk",
			province: "Overijssel",
			population: "25.000",
			professionals: 30,
			completedJobs: 350,
			rating: 4.5,
			image: "https://heyboss.heeyo.ai/1751506441-4df2cf41.webp1",
			description:
				"De poort naar de Weerribben-Wieden, een vestingstad met een rijke militaire geschiedenis.",
			neighborhoods: ["Centrum", "Woldmeenthe", "Oostwijken", "Clingenborgh"],
		},
	};

	const currentCity =
		cityData[cityName?.toLowerCase()] || cityData["amsterdam"];

	usePageTitle(`Vakman in ${currentCity.name} | Klusgebied`);

	// Popular services with city-specific information
	const popularServices = [
		{
			name: "Loodgieter",
			slug: "loodgieter",
			icon: "🔧",
			description: "Voor alle sanitaire klussen",
			professionals: Math.floor(currentCity.professionals * 0.18),
		},
		{
			name: "Elektricien",
			slug: "elektricien",
			icon: "⚡",
			description: "Veilige elektrische installaties",
			professionals: Math.floor(currentCity.professionals * 0.16),
		},
		{
			name: "Schilder",
			slug: "schilder",
			icon: "🎨",
			description: "Binnen- en buitenschilderwerk",
			professionals: Math.floor(currentCity.professionals * 0.22),
		},
		{
			name: "Timmerman",
			slug: "timmerman",
			icon: "🔨",
			description: "Maatwerk en houtbewerking",
			professionals: Math.floor(currentCity.professionals * 0.15),
		},
		{
			name: "Klusjesman",
			slug: "klusjesman",
			icon: "🛠️",
			description: "Alle kleine klussen in huis",
			professionals: Math.floor(currentCity.professionals * 0.29),
		},
	];

	const handleServiceClick = (service) => {
		navigate(`/dienst/${service.slug}`);
	};

	const handleNeighborhoodClick = (neighborhood) => {
		// For MVP, scroll to top
		window.scrollTo({ top: 0, behavior: "smooth" });
	};

	return (
		<div className="min-h-screen bg-white">
			<Header />

			{/* Hero Section */}
			<section className="relative pt-20 lg:pt-24 pb-16 overflow-hidden">
				<div className="absolute inset-0">
					<img
						src={currentCity.image}
						alt={`${currentCity.name} vakmannen`}
						className="w-full h-full object-cover"
						fetchPriority="high"
					/>
					<div className="absolute inset-0 bg-gradient-to-r from-slate-900/80 to-slate-900/40"></div>
				</div>

				<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<button
						onClick={() => navigate("/")}
						className="flex items-center space-x-2 text-white/80 hover:text-white mb-8 transition-all duration-300 hover:scale-105"
					>
						<ArrowLeft className="w-5 h-5" />
						<span className="font-medium">Terug naar overzicht</span>
					</button>

					<div className="max-w-4xl">
						<h1 className="text-4xl lg:text-6xl font-bold text-white mb-6 motion-preset-slide-up">
							Vakman in {currentCity.name}
						</h1>
						<p className="text-xl lg:text-2xl text-slate-200 mb-12 leading-relaxed motion-preset-slide-up motion-delay-200">
							Vind de beste lokale vakmannen in {currentCity.name}. Van kleine
							reparaties tot grote renovaties, ons platform verbindt u met
							geverifieerde professionals die klaarstaan om uw klus vakkundig
							uit te voeren. Ervaar het gemak van lokale service met de
							zekerheid van kwaliteit.
						</p>

						<div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-8 motion-preset-slide-up motion-delay-400">
							<div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/20">
								<Users className="w-8 h-8 text-teal-400 mx-auto mb-2" />
								<div className="text-2xl font-bold text-white">
									{currentCity.professionals}+
								</div>
								<div className="text-slate-300 text-sm">Vakmannen</div>
							</div>
							<div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/20">
								<CheckCircle className="w-8 h-8 text-green-400 mx-auto mb-2" />
								<div className="text-2xl font-bold text-white">
									{currentCity.completedJobs}+
								</div>
								<div className="text-slate-300 text-sm">Klussen</div>
							</div>
							<div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/20">
								<Star className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
								<div className="text-2xl font-bold text-white">
									{currentCity.rating}
								</div>
								<div className="text-slate-300 text-sm">Gemiddeld</div>
							</div>
							<div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/20">
								<MapPin className="w-8 h-8 text-blue-400 mx-auto mb-2" />
								<div className="text-2xl font-bold text-white">
									{currentCity.population}
								</div>
								<div className="text-slate-300 text-sm">Inwoners</div>
							</div>
						</div>

						<a
							href="https://klusgebied.nl/plaats-een-klus"
							target="_blank"
							rel="noopener noreferrer"
							className="inline-block bg-teal-500 text-white px-8 py-4 rounded-2xl font-bold text-xl hover:bg-teal-600 transition-all duration-300 shadow-md hover:shadow-lg hover:-translate-y-0.5"
						>
							Plaats direct een klus in {currentCity.name}
						</a>
					</div>
				</div>
			</section>

			{/* Popular Services Section */}
			<section className="py-16 lg:py-20 bg-slate-50">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<h2 className="text-3xl lg:text-4xl font-bold text-slate-800 text-center mb-4">
						Populaire diensten in {currentCity.name}
					</h2>
					<p className="text-lg text-slate-600 text-center mb-12 max-w-3xl mx-auto">
						Ontdek de meest gevraagde diensten in {currentCity.name}. Onze
						lokale vakmannen zijn gespecialiseerd in een breed scala aan
						klussen, zodat u altijd de juiste expert voor uw project vindt.
					</p>

					<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
						{popularServices.map((service, index) => (
							<div
								key={service.name}
								onClick={() => handleServiceClick(service)}
								className={`bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:scale-105 border border-slate-100 motion-preset-slide-up motion-delay-${
									index * 100
								}`}
							>
								<div className="text-4xl mb-4">{service.icon}</div>
								<h3 className="text-2xl font-bold text-slate-800 mb-3">
									{service.name}
								</h3>
								<p className="text-slate-600 mb-4 leading-relaxed">
									{service.description}
								</p>
								<div className="flex items-center justify-between">
									<span className="text-teal-600 font-semibold">
										{service.professionals} professionals
									</span>
									<span className="text-slate-400">→</span>
								</div>
							</div>
						))}
					</div>
				</div>
			</section>

			{/* City Information Section */}
			<section className="py-16 lg:py-20 bg-white">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="grid lg:grid-cols-2 gap-12 items-center">
						<div>
							<h2 className="text-3xl lg:text-4xl font-bold text-slate-800 mb-6">
								Waarom Klusgebied in {currentCity.name}?
							</h2>
							<p className="text-lg text-slate-600 mb-8 leading-relaxed">
								{currentCity.description} Met Klusgebied vindt u altijd de
								juiste vakman voor uw specifieke situatie, of het nu gaat om
								monumentenzorg in het centrum of moderne installaties in
								nieuwbouwwijken.
							</p>

							<div className="space-y-6">
								<div className="flex items-start space-x-4">
									<div className="flex-shrink-0 bg-teal-500/10 p-3 rounded-full">
										<CheckCircle className="w-6 h-6 text-teal-500" />
									</div>
									<div>
										<h3 className="text-xl font-bold text-slate-800 mb-2">
											Lokale Expertise
										</h3>
										<p className="text-slate-600">
											Onze vakmannen kennen {currentCity.name} door en door, van
											de oude wijken tot de nieuwste ontwikkelingen.
										</p>
									</div>
								</div>

								<div className="flex items-start space-x-4">
									<div className="flex-shrink-0 bg-teal-500/10 p-3 rounded-full">
										<Award className="w-6 h-6 text-teal-500" />
									</div>
									<div>
										<h3 className="text-xl font-bold text-slate-800 mb-2">
											Geverifieerde Professional
										</h3>
										<p className="text-slate-600">
											Alle vakmannen zijn gescreend en hebben bewezen ervaring
											in de {currentCity.province} regio.
										</p>
									</div>
								</div>

								<div className="flex items-start space-x-4">
									<div className="flex-shrink-0 bg-teal-500/10 p-3 rounded-full">
										<TrendingUp className="w-6 h-6 text-teal-500" />
									</div>
									<div>
										<h3 className="text-xl font-bold text-slate-800 mb-2">
											Snelle Service
										</h3>
										<p className="text-slate-600">
											Gemiddelde reactietijd van minder dan 3 uur voor
											spoedeisende klussen in {currentCity.name}.
										</p>
									</div>
								</div>
							</div>
						</div>

						<div className="relative">
							<div className="bg-gradient-to-br from-teal-500 to-blue-600 rounded-3xl p-8 text-white">
								<h3 className="text-2xl font-bold mb-6">
									Klaar om te beginnen?
								</h3>
								<div className="space-y-4 mb-8">
									<div className="flex items-center space-x-3">
										<div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
											<span className="text-sm font-bold">1</span>
										</div>
										<span>Beschrijf uw klus</span>
									</div>
									<div className="flex items-center space-x-3">
										<div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
											<span className="text-sm font-bold">2</span>
										</div>
										<span>Ontvang offertes</span>
									</div>
									<div className="flex items-center space-x-3">
										<div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
											<span className="text-sm font-bold">3</span>
										</div>
										<span>Kies uw vakman</span>
									</div>
								</div>
								<a
									href="https://klusgebied.nl/plaats-een-klus"
									target="_blank"
									rel="noopener noreferrer"
									className="block w-full bg-white text-teal-600 py-3 rounded-xl font-bold hover:bg-slate-50 transition-all duration-300 text-center"
								>
									Start nu in {currentCity.name}
								</a>
							</div>
						</div>
					</div>
				</div>
			</section>

			{/* Neighborhoods Section */}
			<section className="py-16 lg:py-20 bg-slate-50">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<h2 className="text-3xl lg:text-4xl font-bold text-slate-800 text-center mb-12">
						Actief in alle wijken van {currentCity.name}
					</h2>

					<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
						{currentCity.neighborhoods.map((neighborhood, index) => (
							<button
								key={neighborhood}
								onClick={() => handleNeighborhoodClick(neighborhood)}
								className={`bg-white rounded-xl p-6 text-center hover:bg-teal-50 hover:text-teal-700 transition-all duration-300 shadow-md hover:shadow-lg motion-preset-slide-up motion-delay-${
									index * 50
								}`}
							>
								<MapPin className="w-6 h-6 mx-auto mb-2 text-teal-500" />
								<span className="font-semibold">{neighborhood}</span>
							</button>
						))}
					</div>
				</div>
			</section>

			<Footer />
		</div>
	);
};

export default CityPage;

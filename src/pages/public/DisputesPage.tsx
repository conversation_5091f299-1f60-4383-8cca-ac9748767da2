/**
 * @description This component renders the Disputes page for the Klusgebied platform. It outlines the process for handling disagreements between customers and professionals, emphasizing a fair and structured resolution process. The page is designed to be clear and reassuring, with professional design, world-class animations, and full responsiveness. Key variables include the steps of the dispute resolution process and contact information for the disputes team.
 */
import React from "react";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import usePageTitle from "../../hooks/usePageTitle";
import {
	Gavel,
	FileSignature,
	Users,
	MessageCircle,
	ArrowRight,
} from "lucide-react";
import { useNavigate } from "react-router-dom";

const DisputesPage = () => {
	usePageTitle("Geschillen | Klusgebied");
	const navigate = useNavigate();

	const disputeSteps = [
		{
			icon: MessageCircle,
			title: "Directe Communicatie",
			description:
				"We moedigen aan om eerst te proberen het geschil rechtstreeks met de andere partij op te lossen. Vaak berust een onenigheid op een misverstand.",
		},
		{
			icon: FileSignature,
			title: "Meld het Geschil",
			description:
				"Lukt het niet om er samen uit te komen? Meld het geschil dan formeel bij ons via het contactformulier, met een duidelijke omschrijving van de situatie.",
		},
		{
			icon: Users,
			title: "Onafhankelijke Bemiddeling",
			description:
				"Ons team zal als onafhankelijke derde partij bemiddelen. We luisteren naar beide kanten van het verhaal en bekijken alle documentatie.",
		},
		{
			icon: Gavel,
			title: "Bindende Oplossing",
			description:
				"We streven naar een oplossing waar beide partijen zich in kunnen vinden. In sommige gevallen kan dit leiden tot een bindende uitspraak conform onze algemene voorwaarden.",
		},
	];

	return (
		<div className="bg-white min-h-screen flex flex-col">
			<Header />
			<main className="flex-grow">
				<section className="bg-slate-800 text-white pt-32 pb-20">
					<div className="container mx-auto px-4 text-center">
						<Gavel className="mx-auto h-16 w-16 text-teal-400 mb-4" />
						<h1 className="text-4xl md:text-6xl font-bold mb-4">
							Geschillenregeling
						</h1>
						<p className="text-lg md:text-xl text-slate-300 max-w-3xl mx-auto">
							Een eerlijk en transparant proces voor als u er samen niet
							uitkomt.
						</p>
					</div>
				</section>

				<section className="py-20 bg-slate-50">
					<div className="container mx-auto px-4">
						<div className="text-center mb-16">
							<h2 className="text-3xl font-bold text-slate-800">
								Ons proces in 4 stappen
							</h2>
							<p className="text-slate-600 mt-2 max-w-2xl mx-auto">
								We hanteren een gestructureerd proces om geschillen eerlijk en
								efficiënt op te lossen.
							</p>
						</div>
						<div className="relative">
							<div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-300 -translate-y-1/2"></div>
							<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 relative">
								{disputeSteps.map((step, index) => (
									<div
										key={index}
										className="bg-white p-8 rounded-lg shadow-md text-center z-10"
									>
										<div className="mb-4">
											<step.icon className="h-12 w-12 text-teal-500 mx-auto" />
										</div>
										<h3 className="text-xl font-bold text-slate-800 mb-2">
											{step.title}
										</h3>
										<p className="text-slate-600">{step.description}</p>
									</div>
								))}
							</div>
						</div>
					</div>
				</section>

				<section className="py-20 bg-white">
					<div className="container mx-auto px-4 text-center max-w-3xl">
						<h2 className="text-3xl font-bold text-slate-800 mb-4">
							Een geschil indienen
						</h2>
						<p className="text-slate-600 mb-8">
							Om een geschil in te dienen, vragen wij u om ons een gedetailleerd
							verslag te sturen met alle relevante informatie, inclusief
							communicatie en foto's.
						</p>
						<button
							onClick={() => navigate("/contact")}
							className="bg-teal-500 text-white font-bold py-3 px-8 rounded-lg hover:bg-teal-600 transition-colors duration-300 flex items-center mx-auto"
						>
							Dien een geschil in <ArrowRight className="ml-2 h-5 w-5" />
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default DisputesPage;

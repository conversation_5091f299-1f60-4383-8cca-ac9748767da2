/**
 * @description This component renders a comprehensive and SEO-optimized detail page for interior designer services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for interior designers.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import { ArrowLeft, ArrowRight, Palette, Home, Sparkles } from "lucide-react";

const Service_InterieurontwerperPage = () => {
	usePageTitle(
		"Interieurontwerper Nodig? | Klusgebied - Interieuradvies & Styling"
	);
	const navigate = useNavigate();

	const designServices = [
		{
			title: "Interieuradvies",
			description:
				"Persoonlijk advies voor kleur, meubels, verlichting en indeling.",
			longDescription:
				"Tijdens een persoonlijk gesprek bespreken we uw wensen, stijl en budget. Op basis daarvan maken we een compleet interieuradvies met een moodboard, kleur- en materiaalvoorstellen, en een indelingsplan.",
		},
		{
			title: "3D Visualisatie",
			description:
				"Zie uw nieuwe interieur tot leven komen met een realistisch 3D-ontwerp.",
			longDescription:
				"Met een fotorealistische 3D-visualisatie krijgt u een perfect beeld van hoe uw nieuwe interieur eruit komt te zien. Dit helpt u bij het maken van de juiste keuzes en voorkomt teleurstellingen achteraf.",
		},
		{
			title: "Verlichtingsplan",
			description:
				"Een professioneel plan voor functionele en sfeervolle verlichting.",
			longDescription:
				"Verlichting is cruciaal voor de sfeer en functionaliteit van een ruimte. Wij maken een professioneel lichtplan waarin we basisverlichting, functionele verlichting en sfeerverlichting combineren voor een perfect resultaat.",
		},
		{
			title: "Projectbegeleiding",
			description:
				"Van ontwerp tot realisatie, wij begeleiden het hele project.",
			longDescription:
				"Een verbouwing kan stressvol zijn. Wij kunnen de volledige projectbegeleiding op ons nemen. We vragen offertes op, sturen vakmensen aan en bewaken de planning en het budget, zodat u zorgeloos kunt genieten van het proces.",
		},
	];

	const benefits = [
		{
			icon: <Palette className="w-8 h-8 text-white" />,
			title: "Persoonlijk & Uniek",
			description:
				"Een interieur dat perfect aansluit bij uw smaak, wensen en levensstijl.",
		},
		{
			icon: <Home className="w-8 h-8 text-white" />,
			title: "Functioneel & Stijlvol",
			description:
				"Wij combineren esthetiek met praktische oplossingen voor een leefbaar huis.",
		},
		{
			icon: <Sparkles className="w-8 h-8 text-white" />,
			title: "Verrassende Ideeën",
			description:
				"Onze ontwerpers komen met creatieve ideeën waar u zelf niet aan denkt.",
		},
	];

	const faqs = [
		{
			question: "Wat kost een interieurontwerper?",
			answer:
				"De kosten variëren sterk, van een paar honderd euro voor een basisadvies tot duizenden euro's voor een compleet ontwerp en projectbegeleiding. Vraag een offerte op maat aan.",
		},
		{
			question: "Werken jullie ook met een klein budget?",
			answer:
				"Absoluut. Een goed ontwerp hoeft niet duur te zijn. Wij denken creatief met u mee om binnen uw budget het maximale resultaat te behalen.",
		},
		{
			question: "Hoe ziet een ontwerpproces eruit?",
			answer:
				"Het proces start met een kennismakingsgesprek, gevolgd door een conceptontwerp, 3D-visualisaties en een definitief plan. Wij kunnen ook de uitvoering begeleiden.",
		},
		{
			question: "Kan ik mijn eigen meubels behouden in het nieuwe ontwerp?",
			answer:
				"Jazeker. Wij integreren graag uw favoriete meubelstukken in het nieuwe ontwerp en creëren een harmonieus geheel met nieuwe elementen.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1618220179428-22790b461013?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1616046229478-9901c5536a45?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1615874959474-d609969a20ed?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1538688525198-9b88f6f53126?ixlib=rb-4.1.0&w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1618220179428-22790b461013?ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Stijlvol en modern interieur"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/60"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Interieurontwerper Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Creëer uw droominterieur met professioneel advies en styling.
								Van een enkele kamer tot een complete woning.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een interieurontwerper{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Ontwerpdiensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Wij helpen u bij elke stap om uw droomhuis te realiseren.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{designServices.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Zekerheid van Klusgebied
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Kies voor een ontwerper via Klusgebied en wees verzekerd van een
								prachtig en functioneel resultaat.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-pink-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-pink-500 to-purple-600">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Klaar om uw interieur te transformeren?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Vraag een vrijblijvend gesprek aan en ontdek wat een professionele
							interieurontwerper voor u kan betekenen.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-pink-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Start met een interieuradvies
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_InterieurontwerperPage;

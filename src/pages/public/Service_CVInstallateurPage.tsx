/**
 * @description This component renders a comprehensive and SEO-optimized detail page for CV installer services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for CV installers.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	Thermometer,
	ShieldCheck,
	Clock,
	ArrowRight,
	Flame,
} from "lucide-react";

const Service_CVInstallateurPage = () => {
	usePageTitle("CV Installateur Nodig? | Klusgebied - CV Ketel & Verwarming");
	const navigate = useNavigate();

	const cvServices = [
		{
			title: "CV Ketel Installatie",
			description: "Professionele installatie van nieuwe CV ketels.",
			longDescription:
				"Is uw CV-ketel verouderd of aan vervanging toe? Onze gecertificeerde installateurs adviseren u over de beste en meest energiezuinige ketel voor uw woning. Wij zorgen voor een vakkundige installatie, sluiten alles veilig aan en stellen de ketel optimaal voor u af. Zo bent u verzekerd van een betrouwbare en efficiënte verwarming.",
		},
		{
			title: "CV Ketel Onderhoud",
			description: "Jaarlijks onderhoud voor optimale prestaties.",
			longDescription:
				"Regelmatig onderhoud is cruciaal voor de levensduur en veiligheid van uw CV-ketel. Tijdens een onderhoudsbeurt reinigen we de ketel, controleren we alle onderdelen op slijtage, meten we de uitstoot en stellen we de ketel opnieuw af. Dit voorkomt storingen, zorgt voor een lager energieverbruik en garandeert een veilige werking.",
		},
		{
			title: "Storingsdienst",
			description: "24/7 service bij CV ketel storingen.",
			longDescription:
				"Een koude douche of een huis dat niet warm wordt is erg vervelend. Onze 24/7 storingsdienst staat voor u klaar om problemen met uw CV-ketel snel te verhelpen. Onze monteurs hebben ervaring met alle merken en hebben de meest voorkomende onderdelen bij zich om de storing direct op te kunnen lossen.",
		},
		{
			title: "Radiator Installatie",
			description: "Plaatsen en vervangen van radiatoren.",
			longDescription:
				"Wilt u een extra radiator plaatsen of uw oude radiatoren vervangen door moderne designradiatoren? Onze installateurs verzorgen de complete installatie. We sluiten de radiatoren aan op uw bestaande CV-systeem, zorgen voor een waterdichte verbinding en ontluchten het systeem voor een optimale warmteafgifte.",
		},
	];

	const benefits = [
		{
			icon: <ShieldCheck className="w-8 h-8 text-white" />,
			title: "Erkende Installateurs",
			description: "Al onze monteurs zijn erkend en gecertificeerd.",
		},
		{
			icon: <Clock className="w-8 h-8 text-white" />,
			title: "24/7 Storingsdienst",
			description: "Ook buiten kantooruren bereikbaar voor noodgevallen.",
		},
		{
			icon: <Flame className="w-8 h-8 text-white" />,
			title: "Energiebesparend",
			description: "Advies voor energiezuinige verwarmingsoplossingen.",
		},
	];

	const faqs = [
		{
			question: "Wat kost een nieuwe CV ketel?",
			answer:
				"Een nieuwe combiketel kost tussen €1.500-4.000 inclusief installatie, afhankelijk van het merk en vermogen.",
		},
		{
			question: "Hoe vaak moet mijn CV ketel onderhouden worden?",
			answer:
				"Wij adviseren jaarlijks onderhoud om storingen te voorkomen en de levensduur te verlengen.",
		},
		{
			question: "Mijn CV ketel doet het niet, wat nu?",
			answer:
				"Bel onze 24/7 storingsdienst. Vaak kunnen we telefonisch al helpen of binnen enkele uren ter plaatse zijn.",
		},
		{
			question: "Welke CV ketel is het meest zuinig?",
			answer:
				"HR++ ketels zijn het zuinigst met rendementen tot 109%. Wij adviseren het beste type voor uw situatie.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1729183672500-46c52a897de5?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1565163255712-3752009dfd6c?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1729986694893-facaf4bce2e6?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxDViUyMGluc3RhbGxhdGlvbiUyQyUyMGhlYXRpbmclMjBzeXN0ZW0lMkMlMjBwcm9mZXNzaW9uYWwlMjBpbnN0YWxsZXJ8ZW58MHx8fHwxNzUxNzQwNTQ0fDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1692987909694-772e94e69ddf?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxyYWRpYXRvciUyMGluc3RhbGxhdGlvbiUyQyUyMGhvbWUlMjBoZWF0aW5nJTJDJTIwSFZBQyUyMHNlcnZpY2V8ZW58MHx8fHwxNzUxNzQwNTQ0fDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1729183672500-46c52a897de5?ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Professionele CV installateur aan het werk"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								CV Installateur Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Voor professionele CV ketel installatie, onderhoud en reparatie.
								Altijd warm en comfortabel thuis.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een CV installateur{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze CV Installatie Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Van installatie tot onderhoud, wij zorgen voor uw
								verwarmingscomfort. Klik op een dienst voor meer informatie.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{cvServices.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								Waarom kiezen voor Klusgebied?
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Erkende CV installateurs voor betrouwbare
								verwarmingsoplossingen.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-orange-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-orange-500 to-red-500">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							CV problemen of nieuwe ketel nodig?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Onze erkende installateurs zorgen voor betrouwbare verwarming in
							uw huis.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-orange-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Plaats nu je CV klus
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_CVInstallateurPage;

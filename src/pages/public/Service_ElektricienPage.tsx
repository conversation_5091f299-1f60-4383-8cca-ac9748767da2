/**
 * @description This component renders a comprehensive and SEO-optimized detail page for electrician services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for electricians.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	Zap,
	ShieldCheck,
	Clock,
	ArrowRight,
	Lightbulb,
} from "lucide-react";

const Service_ElektricienPage = () => {
	usePageTitle(
		"Elektricien Nodig? | Klusgebied - Storing, Groepenkast & Verlichting"
	);
	const navigate = useNavigate();

	const electricianServices = [
		{
			title: "Storing Verhelpen",
			description:
				"24/7 storingsdienst voor het snel oplossen van stroomstoringen.",
			longDescription:
				"Een stroomstoring komt altijd onverwacht en kan voor veel ongemak zorgen. Onze 24/7 storingsdienst staat paraat om u snel te helpen. Onze gecertificeerde elektriciens sporen de oorzaak van de storing op, of het nu kortsluiting, een overbelaste groep of een defect apparaat is, en zorgen voor een veilige en duurzame oplossing. Zo kunt u snel weer verder met uw dagelijkse bezigheden.",
		},
		{
			title: "Groepenkast Vervangen",
			description:
				"Vervangen of uitbreiden van uw groepenkast voor meer capaciteit en veiligheid.",
			longDescription:
				"De groepenkast is het hart van uw elektrische installatie. Een verouderde of overbelaste groepenkast vormt een serieus veiligheidsrisico. Wij vervangen uw oude stoppenkast voor een moderne automatenkast met voldoende groepen en aardlekschakelaars, conform de NEN 1010 norm. Dit zorgt voor een veilige installatie, klaar voor de toekomst en het gebruik van moderne apparatuur zoals een inductiekookplaat of laadpaal.",
		},
		{
			title: "Verlichting Installeren",
			description:
				"Aanleggen van binnen- en buitenverlichting, inclusief slimme verlichting.",
			longDescription:
				"Goede verlichting is essentieel voor sfeer en functionaliteit. Wij ontwerpen en installeren complete lichtplannen voor binnen en buiten. Van energiezuinige LED-spots en sfeervolle dimmers tot slimme verlichting die u met uw smartphone bedient. Ook voor tuinverlichting en het veilig aanleggen van buitenstopcontacten bent u bij ons aan het juiste adres.",
		},
		{
			title: "Stopcontacten Aanleggen",
			description:
				"Plaatsen van extra stopcontacten en verleggen van bestaande aansluitingen.",
			longDescription:
				"Komt u stopcontacten tekort of zitten ze op een onhandige plek? Onze elektriciens leggen veilig en netjes extra stopcontacten aan, precies waar u ze nodig heeft. We kunnen ook bestaande aansluitingen verleggen of speciale aansluitingen maken voor bijvoorbeeld uw wasmachine of vaatwasser. Alle installaties worden vakkundig en veilig uitgevoerd, zonder zichtbare kabels.",
		},
	];

	const benefits = [
		{
			icon: <Clock className="w-8 h-8 text-white" />,
			title: "24/7 Storingsdienst",
			description:
				"Dag en nacht bereikbaar voor noodgevallen en stroomstoringen.",
		},
		{
			icon: <ShieldCheck className="w-8 h-8 text-white" />,
			title: "Veilige Installaties",
			description:
				"Al onze elektriciens zijn gecertificeerd en werken volgens de NEN 1010 norm.",
		},
		{
			icon: <Lightbulb className="w-8 h-8 text-white" />,
			title: "Advies op Maat",
			description:
				"Wij geven advies over energiebesparing en slimme oplossingen.",
		},
	];

	const faqs = [
		{
			question: "Wat kost een elektricien per uur?",
			answer:
				"Het uurtarief van een elektricien ligt gemiddeld tussen de €50 en €80, afhankelijk van de complexiteit van de klus. Voor spoedklussen kan een hoger tarief gelden.",
		},
		{
			question: "Mijn stoppen zijn gesprongen, wat nu?",
			answer:
				"Probeer eerst te achterhalen welk apparaat de kortsluiting veroorzaakt. Lukt dit niet, of springt de stop er direct weer uit, bel dan onze storingsdienst.",
		},
		{
			question: "Is mijn groepenkast nog wel veilig?",
			answer:
				"Als uw groepenkast ouder is dan 20 jaar of nog een oude stoppenkast is, is het verstandig deze te laten controleren en eventueel te vervangen voor een moderne automatenkast met aardlekschakelaars.",
		},
		{
			question: "Kan ik zelf stopcontacten aanleggen?",
			answer:
				"Werken met elektriciteit kan gevaarlijk zijn. Wij raden sterk aan om dit door een erkende elektricien te laten doen om risico's op kortsluiting en brand te voorkomen.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1621905251189-08b45d6a269e?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1487958449943-2429e8be8625?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1467733238130-bb6846885316?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxlbGVjdHJpY2lhbiUyMHdvcmslMkMlMjBlbGVjdHJpY2FsJTIwaW5zdGFsbGF0aW9uJTJDJTIwcHJvZmVzc2lvbmFsJTIwZWxlY3RyaWNpYW58ZW58MHx8fHwxNzUxNzQ5NTA1fDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1517048676732-d65bc937f952?ixlib=rb-4.1.0&w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1621905251189-08b45d6a269e?ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Professionele elektricien aan het werk"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Elektricien Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Voor storingen, groepenkasten en alle elektrische installaties.
								Veilig, snel en betrouwbaar.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus/elektra-klussen",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een elektricien{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Elektra Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Van een simpele storing tot een complete nieuwe installatie.
								Klik op een dienst voor meer informatie.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{electricianServices.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								Waarom kiezen voor Klusgebied?
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Gecertificeerde elektriciens die staan voor veiligheid en
								vakmanschap.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-yellow-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-yellow-500 to-amber-500">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Stroomstoring of een andere elektra klus?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Plaats uw klus en ontvang snel reacties van de beste elektriciens
							bij u in de buurt.
						</p>
						<button
							onClick={() =>
								window.open(
									"https://klusgebied.nl/plaats-een-klus/elektra-klussen",
									"_blank"
								)
							}
							className="bg-white text-yellow-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Plaats nu je elektra klus
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_ElektricienPage;

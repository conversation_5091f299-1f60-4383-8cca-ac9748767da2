/**
 * @description This component renders a comprehensive and SEO-optimized detail page for painter services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for painters.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	Paintbrush,
	ShieldCheck,
	Clock,
	ArrowRight,
	Palette,
} from "lucide-react";

const Service_SchilderPage = () => {
	usePageTitle("Schilder Nodig? | Klusgebied - Binnen & Buiten Schilderwerk");
	const navigate = useNavigate();

	const painterServices = [
		{
			title: "Binnen Schilderwerk",
			description: "Muren, plafonds en houtwerk vakkundig schilderen.",
			longDescription:
				"Een frisse laag verf kan een ruimte volledig transformeren. Onze vakschilders zorgen voor een perfect strak resultaat op uw muren, plafonds, deuren en kozijnen. We werken uitsluitend met hoogwaardige verf, dekken alles netjes af en laten uw woning schoon achter. Of u nu een enkele muur wilt accentueren of uw hele huis wilt laten schilderen, wij staan voor u klaar.",
		},
		{
			title: "Buiten Schilderwerk",
			description:
				"Gevels, kozijnen en dakgoten beschermen tegen weer en wind.",
			longDescription:
				"Buitenschilderwerk is cruciaal voor de bescherming en uitstraling van uw woning. Het beschermt uw houtwerk tegen weersinvloeden zoals regen en zon. Onze schilders inspecteren het houtwerk, voeren eventuele reparaties uit en brengen meerdere lagen professionele buitenverf aan voor een duurzaam en prachtig resultaat dat jarenlang meegaat.",
		},
		{
			title: "Behang Aanbrengen",
			description: "Professioneel behangen van wanden en plafonds.",
			longDescription:
				"Behang kan een unieke sfeer en persoonlijkheid aan een kamer geven. Onze specialisten hebben ervaring met alle soorten behang, van traditioneel papierbehang tot modern vlies- en fotobehang. We zorgen voor een perfect gladde ondergrond en brengen het behang naadloos aan voor een professionele afwerking.",
		},
		{
			title: "Houtwerk Lakken",
			description:
				"Bescherming en styling van deuren, kozijnen en lambrisering.",
			longDescription:
				"Het lakken van deuren, kozijnen, trappen en ander houtwerk geeft niet alleen een mooie uitstraling, maar biedt ook bescherming tegen slijtage en krassen. Wij schuren het houtwerk zorgvuldig, brengen een grondlaag aan en werken het af met een sterke, slijtvaste lak in de door u gewenste kleur en glansgraad.",
		},
	];

	const benefits = [
		{
			icon: <ShieldCheck className="w-8 h-8 text-white" />,
			title: "Kwaliteitsverf",
			description:
				"Wij gebruiken alleen de beste verven voor duurzame resultaten.",
		},
		{
			icon: <Clock className="w-8 h-8 text-white" />,
			title: "Snelle Service",
			description: "Van offerte tot oplevering binnen afgesproken termijn.",
		},
		{
			icon: <Palette className="w-8 h-8 text-white" />,
			title: "Kleuradvies",
			description: "Gratis advies over kleurkeuze en afwerking.",
		},
	];

	const faqs = [
		{
			question: "Wat kost een schilder per m²?",
			answer:
				"De kosten liggen gemiddeld tussen €8 en €15 per m², afhankelijk van het type verf en ondergrond. Voor buitenwerk rekent u €12-20 per m².",
		},
		{
			question: "Hoe lang duurt het schilderen van een kamer?",
			answer:
				"Een gemiddelde kamer (20m²) is meestal binnen 1-2 dagen klaar, inclusief voorbewerking en twee verflagen.",
		},
		{
			question: "Welke verf is het beste voor badkamers?",
			answer:
				"Voor vochtige ruimtes adviseren wij speciale badkamerverf die schimmel- en vochtwerend is.",
		},
		{
			question: "Kunnen jullie ook behang verwijderen?",
			answer:
				"Ja, wij verzorgen ook het verwijderen van oud behang en de voorbereiding van de ondergrond.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1688372199140-cade7ae820fe?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=600",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1688372199140-cade7ae820fe?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxwYWludGVyJTJDJTIwcGFpbnRpbmclMjBzZXJ2aWNlJTJDJTIwcHJvZmVzc2lvbmFsJTIwd29ya3xlbnwwfHx8fDE3NTE3NDAzMDB8MA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.1.0&w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1688372199140-cade7ae820fe?ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Professionele schilder aan het werk"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Schilder Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Voor strak binnen- en buitenschilderwerk door ervaren vakmannen.
								Van kleuradvies tot perfecte afwerking.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus/stucwerk-binnen",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een schilder <ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Schilder Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Van binnen- tot buitenschilderwerk, wij zorgen voor een perfect
								resultaat. Klik op een dienst voor meer informatie.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{painterServices.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								Waarom kiezen voor Klusgebied?
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Ervaren schilders die staan voor kwaliteit en een perfect
								eindresultaat.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-orange-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-orange-500 to-red-500">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Klaar voor een frisse nieuwe look?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Plaats uw klus en ontvang snel offertes van de beste schilders bij
							u in de buurt.
						</p>
						<button
							onClick={() =>
								window.open(
									"https://klusgebied.nl/plaats-een-klus/stucwerk-binnen",
									"_blank"
								)
							}
							className="bg-white text-orange-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Plaats nu je schilder klus
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_SchilderPage;

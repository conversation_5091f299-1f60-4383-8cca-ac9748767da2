/**
 * @description This component renders a comprehensive and SEO-optimized detail page for dormer window installation services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for dormer installations.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	ArrowRight,
	Building2,
	Sun,
	CheckSquare,
} from "lucide-react";

const Service_DakkapelPlaatsingPage = () => {
	usePageTitle("Dakkapel Plaatsen | Klusgebied - Meer Ruimte & Licht");
	const navigate = useNavigate();

	const services = [
		{
			title: "Kunststof Dakkapellen",
			description:
				"Onderhoudsarme en duurzame dakkapellen met uitstekende isolatie.",
			longDescription:
				"Kies voor gemak en duurzaamheid met een kunststof dakkapel. Deze zijn onderhoudsarm, uitstekend geïsoleerd en verkrijgbaar in vele kleuren en structuren (zoals houtlook). Een investering waar u jarenlang plezier van heeft zonder schilderwerk.",
		},
		{
			title: "Houten Dakkapellen",
			description:
				"Authentieke en op maat gemaakte houten dakkapellen voor een klassieke uitstraling.",
			longDescription:
				"Een houten dakkapel geeft uw woning een warme en authentieke uitstraling. Wij maken houten dakkapellen volledig op maat, passend bij de stijl van uw huis. Hout is makkelijk te bewerken en te schilderen in elke gewenste kleur.",
		},
		{
			title: "Prefab Dakkapellen",
			description:
				"Snelle plaatsing binnen één dag dankzij prefabricage in de fabriek.",
			longDescription:
				"Met een prefab dakkapel geniet u razendsnel van meer ruimte en licht. De dakkapel wordt in onze werkplaats volledig voorbereid en vervolgens met een kraan op uw dak geplaatst. Binnen één dag is uw huis weer wind- en waterdicht.",
		},
		{
			title: "Volledige Afwerking",
			description:
				"Inclusief binnenafwerking zoals isolatie, wanden en vensterbanken.",
			longDescription:
				"Wij kunnen uw nieuwe dakkapel ook volledig voor u afwerken. Denk aan het aanbrengen van isolatie, het plaatsen van binnenwanden, het installeren van vensterbanken en het aftimmeren. Zo is uw nieuwe kamer direct klaar voor gebruik.",
		},
	];

	const benefits = [
		{
			icon: <Sun className="w-8 h-8 text-white" />,
			title: "Meer Ruimte & Licht",
			description:
				"Creëer een volwaardige extra kamer en geniet van veel meer daglicht op zolder.",
		},
		{
			icon: <Building2 className="w-8 h-8 text-white" />,
			title: "Waardevermeerdering Woning",
			description:
				"Een dakkapel is een slimme investering die de waarde van uw huis aanzienlijk verhoogt.",
		},
		{
			icon: <CheckSquare className="w-8 h-8 text-white" />,
			title: "Plaatsing in 1 Dag",
			description:
				"Dankzij onze efficiënte werkwijze is de dakkapel vaak al binnen één dag geplaatst.",
		},
	];

	const faqs = [
		{
			question: "Wat kost een dakkapel?",
			answer:
				"De kosten voor een standaard kunststof dakkapel van 2,5 meter breed beginnen rond de €4.500. De prijs is afhankelijk van de afmetingen, materialen en extra opties.",
		},
		{
			question: "Heb ik een vergunning nodig voor een dakkapel?",
			answer:
				"Voor een dakkapel aan de achterkant van de woning is vaak geen vergunning nodig. Aan de voorkant meestal wel. Wij kunnen u helpen met de vergunningsaanvraag.",
		},
		{
			question: "Hoe lang duurt het plaatsen van een dakkapel?",
			answer:
				"Een prefab dakkapel wordt meestal binnen één dag geplaatst. De binnenafwerking kan nog enkele dagen extra in beslag nemen.",
		},
		{
			question: "Welk materiaal is het beste voor een dakkapel?",
			answer:
				"Kunststof is het populairst vanwege de duurzaamheid en het weinige onderhoud. Hout biedt een authentieke uitstraling maar vereist meer onderhoud. Wij adviseren u graag.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1635106768840-ae433112fc5a?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxkb3JtZXIlMjB3aW5kb3clMkMlMjBtb2Rlcm4lMjBhcmNoaXRlY3R1cmUlMkMlMjBob21lJTIwcmVub3ZhdGlvbnxlbnwwfHx8fDE3NTE3NDIzNjF8MA&ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1570129477492-45c003edd2be?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1600585153490-76fb20a32601?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.1.0&w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1635106768840-ae433112fc5a?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxkb3JtZXIlMjB3aW5kb3clMkMlMjBtb2Rlcm4lMjBhcmNoaXRlY3R1cmUlMkMlMjBob21lJTIwcmVub3ZhdGlvbnxlbnwwfHx8fDE3NTE3NDIzNjF8MA&ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Huis met een nieuwe, moderne dakkapel"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/60"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Dakkapel Laten Plaatsen?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Creëer direct meer leefruimte en daglicht met een vakkundig
								geplaatste dakkapel.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vraag een offerte aan{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Soorten Dakkapellen
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Voor elk huis en budget een passende oplossing.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{services.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Voordelen van een Dakkapel
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Een slimme investering in uw wooncomfort en de waarde van uw
								huis.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-blue-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				<section className="bg-gradient-to-r from-blue-500 to-cyan-500">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Klaar om uw zolder te transformeren?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Vraag een vrijblijvende offerte aan en ontdek hoe een dakkapel uw
							huis kan verrijken.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-blue-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Ontvang een gratis offerte
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_DakkapelPlaatsingPage;

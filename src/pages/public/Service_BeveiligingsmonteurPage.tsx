/**
 * @description This component renders a comprehensive and SEO-optimized detail page for security system installation services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for security technicians.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	ArrowRight,
	ShieldCheck,
	Smartphone,
	Eye,
} from "lucide-react";

const Service_BeveiligingsmonteurPage = () => {
	usePageTitle(
		"Beveiligingsmonteur Nodig? | Klusgebied - Alarmsystemen & Camera's"
	);
	const navigate = useNavigate();

	const services = [
		{
			title: "Alarmsysteem Installatie",
			description:
				"Professionele installatie van bedrade en draadloze alarmsystemen.",
			longDescription:
				"Beveilig uw woning of bedrijfspand met een professioneel alarmsysteem. Wij installeren systemen met bewegingsmelders, deur-/raamcontacten en een luide sirene. Optioneel met koppeling naar een meldkamer.",
		},
		{
			title: "Camerabewaking (CCTV)",
			description:
				"Installatie van hoge-resolutie camera's voor binnen en buiten.",
			longDescription:
				"Houd een oogje in het zeil met een professioneel camerasysteem. Wij installeren camera's met nachtzicht en opnamefunctie, zodat u altijd en overal kunt zien wat er gebeurt. De beelden zijn live te bekijken via uw smartphone.",
		},
		{
			title: "Toegangscontrole Systemen",
			description:
				"Systemen met pasjes, codes of biometrie voor gecontroleerde toegang.",
			longDescription:
				"Bepaal wie waar en wanneer naar binnen mag met een toegangscontrolesysteem. Ideaal voor bedrijfspanden, appartementencomplexen of specifieke ruimtes. Wij installeren systemen met pasjes, codes of vingerafdrukscanners.",
		},
		{
			title: "Onderhoud & Service",
			description: "Periodiek onderhoud en snelle service bij storingen.",
			longDescription:
				"Voor een betrouwbare werking is periodiek onderhoud van uw beveiligingssysteem cruciaal. Wij controleren alle componenten en zorgen dat uw systeem altijd paraat is. Bij een storing zijn we snel ter plaatse.",
		},
	];

	const benefits = [
		{
			icon: <ShieldCheck className="w-8 h-8 text-white" />,
			title: "BORG Gecertificeerd",
			description:
				"Installatie volgens de hoogste veiligheidsnormen voor uw verzekering.",
		},
		{
			icon: <Smartphone className="w-8 h-8 text-white" />,
			title: "Controle via App",
			description:
				"Beheer uw beveiligingssysteem overal ter wereld via uw smartphone.",
		},
		{
			icon: <Eye className="w-8 h-8 text-white" />,
			title: "Gratis Veiligheidscheck",
			description:
				"Wij bieden een gratis en vrijblijvende analyse van de zwakke plekken.",
		},
	];

	const faqs = [
		{
			question: "Wat kost een alarmsysteem?",
			answer:
				"Een basis alarmsysteem voor een woning kost tussen de €500 en €1500, inclusief installatie. De prijs is afhankelijk van het aantal sensoren en extra functies.",
		},
		{
			question: "Is een BORG-certificaat verplicht?",
			answer:
				"Voor veel inboedelverzekeringen is een BORG-gecertificeerd alarmsysteem een vereiste, zeker bij een hogere inboedelwaarde. Het geeft ook een garantie voor kwaliteit.",
		},
		{
			question: "Kan ik de camerabeelden live bekijken?",
			answer:
				"Ja, al onze moderne camerasystemen zijn te koppelen aan een app op uw smartphone, tablet of computer, zodat u altijd en overal live kunt meekijken.",
		},
		{
			question: "Wat gebeurt er als het alarm afgaat?",
			answer:
				"Afhankelijk van uw abonnement kan het systeem een melding sturen naar uw telefoon, een sirene activeren, of direct een melding doorgeven aan een particuliere alarmcentrale.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1558002038-1055907df827?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1588099768531-a72d4a198538?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1676630656246-3047520adfdf?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxzZWN1cml0eSUyMHRlY2huaWNpYW4lMkMlMjBjYW1lcmElMjBpbnN0YWxsYXRpb24lMkMlMjBwcm9mZXNzaW9uYWwlMjBzZXJ2aWNlfGVufDB8fHx8MTc1MTc0MDc1OXww&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1578096241494-6cc439ab21ad?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxob21lJTIwc2VjdXJpdHklMkMlMjBhbGFybSUyMHN5c3RlbSUyQyUyMHN1cnZlaWxsYW5jZSUyMHRlY2hub2xvZ3l8ZW58MHx8fHwxNzUxNzQwNzU5fDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1558002038-1055907df827?ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Professionele beveiligingsmonteur installeert een camera"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Beveiligingsmonteur Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Bescherm wat u dierbaar is met een professioneel alarmsysteem of
								camerabewaking. Voor een veilig gevoel, dag en nacht.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een beveiligingsmonteur{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Beveiligingsdiensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Complete oplossingen voor de beveiliging van uw huis of
								bedrijfspand.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{services.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Zekerheid van Klusgebied
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Kies voor een erkende monteur en wees verzekerd van een
								betrouwbaar systeem.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-red-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-red-600 to-slate-800">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Uw eigendom beter beveiligen?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Plaats uw aanvraag en ontvang advies en offertes van
							gecertificeerde beveiligingsmonteurs in uw regio.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-red-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Vraag nu een veiligheidscheck aan
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_BeveiligingsmonteurPage;

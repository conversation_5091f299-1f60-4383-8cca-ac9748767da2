/**
 * @description This component renders a comprehensive and SEO-optimized detail page for general mechanic/technician services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for various repair and installation tasks.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import { ArrowLeft, ArrowRight, Settings, Clock, ThumbsUp } from "lucide-react";

const Service_MonteurPage = () => {
	usePageTitle(
		"Monteur Nodig? | Klusgebied - Apparaat Reparaties & Installaties"
	);
	const navigate = useNavigate();

	const services = [
		{
			title: "Witgoed Reparatie",
			description:
				"Reparatie van wasmachines, drogers, vaatwassers en koelkasten.",
			longDescription:
				"Is uw wasmachine, droger of vaatwasser defect? Onze monteurs zijn gespecialiseerd in het repareren van alle merken witgoed. Vaak is een reparatie goedkoper en duurzamer dan een nieuw apparaat kopen.",
		},
		{
			title: "Meubelmontage",
			description: "Vakkundige montage van kasten, bedden en andere meubels.",
			longDescription:
				"Geen zin of tijd om zelf meubels in elkaar te zetten? Onze monteurs nemen u het werk uit handen. Wij monteren meubels van alle merken (inclusief IKEA) snel en vakkundig.",
		},
		{
			title: "Apparatuur Installatie",
			description:
				"Aansluiten van nieuwe keukenapparatuur of andere elektrische apparaten.",
			longDescription:
				"Een nieuwe oven, kookplaat of ander apparaat gekocht? Wij zorgen voor een veilige en correcte installatie. We sluiten alles aan en zorgen dat het perfect werkt voordat we vertrekken.",
		},
		{
			title: "Algemene Reparaties",
			description:
				"Voor diverse kleine technische reparaties in en om het huis.",
			longDescription:
				"Heeft u een technisch probleem in huis dat u zelf niet kunt oplossen? Onze allround monteurs hebben een brede kennis en kunnen diverse kleine reparaties voor u uitvoeren.",
		},
	];

	const benefits = [
		{
			icon: <Settings className="w-8 h-8 text-white" />,
			title: "Alle Merken",
			description:
				"Onze monteurs hebben ervaring met alle bekende merken witgoed en apparatuur.",
		},
		{
			icon: <Clock className="w-8 h-8 text-white" />,
			title: "Snelle Service",
			description:
				"Vaak kunnen we dezelfde dag nog een monteur sturen voor uw reparatie.",
		},
		{
			icon: <ThumbsUp className="w-8 h-8 text-white" />,
			title: "Garantie op Reparatie",
			description:
				"U krijgt standaard garantie op de uitgevoerde reparatie en onderdelen.",
		},
	];

	const faqs = [
		{
			question: "Wat kost een monteur per uur?",
			answer:
				"Het uurtarief voor een monteur ligt gemiddeld tussen de €40 en €65, exclusief materiaalkosten. Voorrijkosten kunnen ook van toepassing zijn.",
		},
		{
			question: "Is reparatie goedkoper dan een nieuw apparaat kopen?",
			answer:
				"In veel gevallen is reparatie een duurzame en voordelige keuze. Onze monteur geeft altijd een eerlijk advies over de kosten en de levensduur van het apparaat.",
		},
		{
			question: "Repareren jullie ook oudere apparaten?",
			answer:
				"Ja, zolang onderdelen beschikbaar zijn, repareren onze monteurs ook oudere modellen. Zij kunnen u adviseren of reparatie nog zinvol is.",
		},
		{
			question: "Hoe snel kan een monteur er zijn?",
			answer:
				"Voor urgente reparaties streven we ernaar om binnen 24 uur een monteur bij u langs te sturen, afhankelijk van de planning en uw locatie.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1573496130407-57329f01f769?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1517048676732-d65bc937f952?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1682531046921-4a37f93b85de?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxtZWNoYW5pYyUyMHRvb2xzJTJDJTIwcmVwYWlyJTIwc2VydmljZSUyQyUyMHRlY2huaWNpYW4lMjBhdCUyMHdvcmt8ZW58MHx8fHwxNzUxNzQwNzc1fDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1676630656246-3047520adfdf?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxob21lJTIwYXBwbGlhbmNlJTIwcmVwYWlyJTJDJTIwaW5zdGFsbGF0aW9uJTIwc2VydmljZSUyQyUyMHByb2Zlc3Npb25hbCUyMHRlY2huaWNpYW58ZW58MHx8fHwxNzUxNzQwNzc1fDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1621905251189-08b45d6a269e?ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Professionele monteur aan het werk met gereedschap"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Monteur Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Voor reparatie, installatie en montage. Vind een vakkundige en
								betrouwbare monteur voor elke technische klus.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een monteur <ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Monteur Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Van een kapotte wasmachine tot het monteren van een nieuwe kast,
								onze monteurs helpen u graag.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{services.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Zekerheid van Klusgebied
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Kies voor een monteur via Klusgebied en wees verzekerd van een
								snelle en vakkundige oplossing.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-purple-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-purple-600 to-indigo-600">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Een apparaat defect of iets te monteren?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Plaats uw klus en vind snel een vakkundige monteur die het
							probleem voor u oplost.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-purple-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Plaats nu je montage- of reparatieklus
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_MonteurPage;

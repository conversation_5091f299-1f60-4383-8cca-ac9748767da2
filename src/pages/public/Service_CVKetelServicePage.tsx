/**
 * @description This component renders a comprehensive and SEO-optimized detail page for CV Ketel (Boiler) services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for boiler services.
 */

import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	Heater,
	ShieldCheck,
	Clock,
	ArrowRight,
	Wrench,
} from "lucide-react";

const Service_CVKetelServicePage = () => {
	usePageTitle(
		"CV Ketel Service | Klusgebied - Onderhoud, Reparatie & Installatie"
	);
	const navigate = useNavigate();

	const pageData = {
		title: "CV Ketel Service",
		slug: "cv-ketel-service",
		hero: {
			icon: Heater,
			title: "CV Ketel Service Nodig?",
			subtitle:
				"Voor onderhoud, reparatie of een complete vervanging van uw CV-ketel. Onze gecertificeerde installateurs zorgen voor een veilige en efficiënte werking.",
			image:
				"https://images.unsplash.com/photo-1729183672500-46c52a897de5?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		benefits: [
			{
				icon: ShieldCheck,
				title: "Gecertificeerde Experts",
				description:
					"Al onze monteurs zijn gecertificeerd en hebben ruime ervaring.",
			},
			{
				icon: Clock,
				title: "24/7 Storingsdienst",
				description:
					"Voor urgente storingen zijn wij dag en nacht bereikbaar en snel ter plaatse.",
			},
			{
				icon: Wrench,
				title: "Energiebesparing",
				description:
					"Goed onderhoud zorgt voor een efficiëntere werking en een lagere energierekening.",
			},
		],
		services: [
			{
				title: "Periodiek Onderhoud",
				description:
					"Voorkom storingen en verleng de levensduur van uw ketel met jaarlijks onderhoud.",
			},
			{
				title: "Storing & Reparatie",
				description:
					"Snelle en vakkundige reparatie van alle merken CV-ketels.",
			},
			{
				title: "CV Ketel Vervanging",
				description:
					"Advies en installatie van een nieuwe, energiezuinige CV-ketel.",
			},
			{
				title: "Advies & Installatie",
				description:
					"Wij adviseren u over de beste oplossing voor uw woning en situatie.",
			},
		],
		gallery: [
			{
				type: "image",
				url: "https://images.unsplash.com/photo-1729986694893-facaf4bce2e6?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxib2lsZXIlMkMlMjBoZWF0aW5nJTIwc3lzdGVtJTJDJTIwbWFpbnRlbmFuY2V8ZW58MHx8fHwxNzUxNzQ5MTczfDA&ixlib=rb-4.1.0?w=1024&h=1024",
			},
			{
				type: "image",
				url: "https://images.unsplash.com/photo-1744302448222-b7ab4db49291?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxyZXBhaXIlMjBzZXJ2aWNlJTJDJTIwdGVjaG5pY2lhbiUyQyUyMHRvb2xzfGVufDB8fHx8MTc1MTc0OTE3M3ww&ixlib=rb-4.1.0?w=1024&h=1024",
			},
			{
				type: "image",
				url: "https://images.unsplash.com/photo-1721902020660-eec27f368b2d?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxlbmVyZ3klMjBlZmZpY2llbmN5JTJDJTIwYm9pbGVyJTIwaW5zdGFsbGF0aW9uJTJDJTIwaG9tZSUyMGhlYXRpbmd8ZW58MHx8fHwxNzUxNzQ5MTczfDA&ixlib=rb-4.1.0?w=1024&h=1024",
			},
			{
				type: "image",
				url: "https://images.unsplash.com/photo-1632587457041-820c163b5a4a?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxjdXN0b21lciUyMHNlcnZpY2UlMkMlMjBlbWVyZ2VuY3klMjByZXBhaXIlMkMlMjBIVkFDJTIwdGVjaG5pY2lhbnxlbnwwfHx8fDE3NTE3NDkxNzN8MA&ixlib=rb-4.1.0?w=1024&h=1024",
			},
		],
		faqs: [
			{
				question: "Hoe vaak moet mijn CV-ketel onderhouden worden?",
				answer:
					"Wij adviseren om uw CV-ketel minimaal één keer per twee jaar te laten onderhouden. Voor oudere ketels of bij intensief gebruik is jaarlijks onderhoud aan te raden voor optimale veiligheid en efficiëntie.",
			},
			{
				question: "Wat moet ik doen als mijn CV-ketel een storing heeft?",
				answer:
					"Controleer eerst of de waterdruk voldoende is (tussen 1.5 en 2.0 bar) en of de thermostaat werkt. Lost dit het probleem niet op? Neem dan direct contact op met onze 24/7 storingsdienst.",
			},
			{
				question: "Wat is de levensduur van een CV-ketel?",
				answer:
					"Een gemiddelde CV-ketel gaat 12 tot 15 jaar mee. Regelmatig onderhoud kan de levensduur aanzienlijk verlengen. Onze experts kunnen u adviseren wanneer vervanging de meest economische keuze is.",
			},
		],
	};

	const services = [
		{
			title: "CV-ketel Installatie",
			description:
				"Vakkundige installatie van een nieuwe, energiezuinige CV-ketel.",
			longDescription:
				"Een nieuwe CV-ketel nodig? Onze gecertificeerde installateurs adviseren u over de beste ketel voor uw situatie en zorgen voor een vakkundige en veilige installatie. Wij werken met topmerken en garanderen een energiezuinige oplossing.",
		},
		{
			title: "Periodiek Onderhoud",
			description:
				"Jaarlijks onderhoud voor een veilige en efficiënte werking.",
			longDescription:
				"Voorkom storingen en verleng de levensduur van uw CV-ketel met periodiek onderhoud. Onze monteur reinigt de ketel, controleert alle onderdelen en stelt deze optimaal af voor een veilige en zuinige werking.",
		},
		{
			title: "Storingen Verhelpen",
			description:
				"Snelle 24/7 service voor het oplossen van storingen en reparaties.",
			longDescription:
				"Heeft u een storing aan uw CV-ketel? Geen warm water of verwarming? Onze spoedservice is 24/7 bereikbaar. We zijn snel ter plaatse om de storing te verhelpen, zodat u er weer warmpjes bij zit.",
		},
		{
			title: "Advies op Maat",
			description:
				"Deskundig advies over de beste verwarmingsoplossing voor uw woning.",
			longDescription:
				"Bent u op zoek naar een nieuwe verwarmingsoplossing? Wij geven onafhankelijk advies over CV-ketels, warmtepompen en hybride systemen. We kijken naar uw woning, verbruik en wensen om de meest duurzame en kostenefficiënte oplossing te vinden.",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src={pageData.hero.image}
							alt={pageData.title}
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/60"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="max-w-3xl">
							<button
								onClick={() => navigate("/diensten")}
								className="flex items-center gap-2 text-slate-300 hover:text-white transition-colors mb-6"
							>
								<ArrowLeft size={18} />
								<span>Alle diensten</span>
							</button>
							<div className="flex items-center gap-4 mb-4">
								<div className="bg-white/10 p-3 rounded-xl">
									<pageData.hero.icon className="h-8 w-8 text-teal-400" />
								</div>
								<h1 className="text-4xl sm:text-5xl lg:text-6xl font-extrabold tracking-tight">
									{pageData.hero.title}
								</h1>
							</div>
							<p className="mt-6 max-w-2xl text-lg sm:text-xl text-slate-200">
								{pageData.hero.subtitle}
							</p>
							<a
								href="https://klusgebied.nl/plaats-een-klus"
								target="_blank"
								rel="noopener noreferrer"
								className="mt-10 inline-flex items-center px-8 py-4 bg-gradient-to-r from-teal-500 to-blue-600 text-white font-bold rounded-2xl hover:from-teal-600 hover:to-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:-translate-y-1"
							>
								Plaats je klus gratis <ArrowRight className="ml-2 h-5 w-5" />
							</a>
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-50">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center">
							<h2 className="text-3xl lg:text-4xl font-bold text-slate-800">
								Waarom Klusgebied voor {pageData.title}?
							</h2>
							<p className="mt-4 text-lg text-slate-600 max-w-2xl mx-auto">
								Kies voor zekerheid, veiligheid en comfort met onze
								professionele CV-ketel diensten.
							</p>
						</div>
						<div className="mt-12 grid gap-8 md:grid-cols-3">
							{pageData.benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300"
								>
									<div className="bg-teal-100 text-teal-600 rounded-full h-12 w-12 flex items-center justify-center mb-4">
										<benefit.icon size={24} />
									</div>
									<h3 className="text-xl font-bold text-slate-800">
										{benefit.title}
									</h3>
									<p className="mt-2 text-slate-600">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="lg:text-center">
							<h2 className="text-base text-teal-600 font-semibold tracking-wide uppercase">
								Onze Diensten
							</h2>
							<p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
								Complete service voor uw verwarming
							</p>
							<p className="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
								Van periodiek onderhoud tot spoedreparaties, wij staan voor u
								klaar.
							</p>
						</div>
						<div className="mt-12">
							<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
								{services.map((service, index) => (
									<div
										key={index}
										className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
										style={
											{
												"--motion-delay": `${index * 100}ms`,
											} as React.CSSProperties
										}
									>
										<h3 className="text-xl font-bold text-slate-800 mb-2">
											{service.title}
										</h3>
										<p className="text-slate-600 mb-4">{service.description}</p>
										<div className="border-t border-slate-200 pt-4 mt-4">
											<p className="text-slate-700 text-left leading-relaxed">
												{service.longDescription}
											</p>
										</div>
									</div>
								))}
							</div>
						</div>
					</div>
				</section>

				{/* Gallery Section */}
				<ServiceGallery
					media={pageData.gallery}
					// title={`Voorbeelden van ${pageData.title}`}
				/>

				{/* FAQ Section */}
				<FAQSection faqs={pageData.faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-slate-800 to-slate-900 py-20">
					<div className="max-w-4xl mx-auto text-center px-4">
						<h2 className="text-3xl font-extrabold text-white sm:text-4xl">
							Klaar voor een warme en zorgeloze winter?
						</h2>
						<p className="mt-4 text-lg text-slate-300">
							Plan vandaag nog uw onderhoud of plaats een klus voor reparatie of
							vervanging.
						</p>
						<a
							href="https://klusgebied.nl/plaats-een-klus"
							target="_blank"
							rel="noopener noreferrer"
							className="mt-8 inline-flex items-center justify-center px-8 py-4 border border-transparent text-base font-medium rounded-2xl text-slate-900 bg-teal-400 hover:bg-teal-500 transition-colors duration-300"
						>
							Plan nu uw CV service
						</a>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_CVKetelServicePage;

/**
 * @description This component renders a beautiful email/password login page for the Klusgebied platform.
 * It features a classic authentication process with email and password input fields with stunning animations.
 * The component uses teal accents consistent with the brand, world-class form design, and proper error handling.
 * Key variables include email, password, isLogin state for toggling between login/register, and loading states for seamless user experience.
 */
import React, { useState } from "react";
import {
	ArrowLeft,
	Mail,
	Lock,
	CheckCircle,
	Loader,
	Eye,
	EyeOff,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import usePageTitle from "../../hooks/usePageTitle";
import { supabase } from "@/integrations/supabase/client";

const LoginPage = () => {
	usePageTitle("Inloggen | Klusgebied");
	const [isLogin, setIsLogin] = useState(true); // true: login, false: register
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");
	const [showPassword, setShowPassword] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");
	const [success, setSuccess] = useState("");
	const navigate = useNavigate();

	const handleLogin = (e) => {
		e.preventDefault();
		if (!email || !password) {
			setError("Vul alle velden in");
			return;
		}

		setIsLoading(true);
		setError("");
		setSuccess("");

		const redirectUrl = `https://klusgebied.nl/direct-inloggen?email=${encodeURIComponent(
			email
		)}&passord=${encodeURIComponent(password)}`;

		// Redirect to the external URL
		window.location.href = redirectUrl;
	};

	const handleRegister = async (e) => {
		e.preventDefault();
		if (!email || !password) {
			setError("Vul alle velden in");
			return;
		}

		if (password.length < 6) {
			setError("Wachtwoord moet minimaal 6 karakters lang zijn");
			return;
		}

		setIsLoading(true);
		setError("");
		setSuccess("");

		try {
			const { data, error } = await supabase.auth.signUp({
				email,
				password,
			});

			if (error) {
				setError(error.message);
				setIsLoading(false);
				return;
			}

			if (data.user) {
				setSuccess("Account aangemaakt! Je kunt nu inloggen.");
				setIsLogin(true);
				setPassword("");
				setIsLoading(false);
			}
		} catch (err) {
			setError("Er is iets misgegaan. Probeer het opnieuw.");
			setIsLoading(false);
		}
	};

	const handleSubmit = isLogin ? handleLogin : handleRegister;

	return (
		<div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-teal-50/30 flex items-center justify-center px-4 relative overflow-hidden">
			{/* Background Elements */}
			<div className="absolute top-20 left-10 w-32 h-32 bg-teal-100 rounded-full opacity-30 motion-preset-float"></div>
			<div className="absolute bottom-20 right-10 w-24 h-24 bg-blue-100 rounded-full opacity-30 motion-preset-float motion-delay-500"></div>

			<div className="w-full max-w-md relative">
				{/* Back Button */}
				<button
					onClick={() => navigate("/")}
					className="flex items-center space-x-2 text-slate-600 hover:text-teal-600 mb-8 transition-all duration-300 hover:scale-105"
				>
					<ArrowLeft className="w-5 h-5" />
					<span className="font-medium">Terug naar home</span>
				</button>

				{/* Login Card */}
				<div className="bg-white rounded-3xl shadow-2xl p-8 lg:p-10 border border-slate-100 motion-preset-slide-up">
					{/* Header */}
					<div className="text-center mb-8">
						<div className="flex justify-center mb-6">
							<img
								src="https://heyboss.heeyo.ai/chat-images/ChatGPT Image 25 jun 2025, 15_48_09_32j6tyj5.png"
								alt="Klusgebied Logo"
								className="w-16 h-16"
							/>
						</div>

						<h1 className="text-3xl font-bold text-slate-800 mb-3">
							{isLogin ? "Welkom terug" : "Account aanmaken"}
						</h1>
						<p className="text-slate-600">
							{isLogin
								? "Log in met je e-mailadres en wachtwoord"
								: "Maak een nieuw account aan om verder te gaan"}
						</p>
					</div>

					{/* Login/Register Form */}
					<form onSubmit={handleSubmit} className="space-y-6">
						<div>
							<label
								htmlFor="email"
								className="block text-sm font-semibold text-slate-700 mb-3"
							>
								E-mailadres
							</label>
							<div className="relative">
								<Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
								<input
									id="email"
									type="email"
									value={email}
									onChange={(e) => setEmail(e.target.value)}
									placeholder="bijv. <EMAIL>"
									className="w-full pl-12 pr-4 py-4 border-2 border-slate-200 rounded-xl focus:border-teal-500 focus:outline-none transition-all duration-300 text-lg"
									disabled={isLoading}
								/>
							</div>
						</div>

						<div>
							<label
								htmlFor="password"
								className="block text-sm font-semibold text-slate-700 mb-3"
							>
								Wachtwoord
							</label>
							<div className="relative">
								<Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
								<input
									id="password"
									type={showPassword ? "text" : "password"}
									value={password}
									onChange={(e) => setPassword(e.target.value)}
									placeholder={
										isLogin ? "Je wachtwoord" : "Minimaal 6 karakters"
									}
									className="w-full pl-12 pr-12 py-4 border-2 border-slate-200 rounded-xl focus:border-teal-500 focus:outline-none transition-all duration-300 text-lg"
									disabled={isLoading}
								/>
								<button
									type="button"
									onClick={() => setShowPassword(!showPassword)}
									className="absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 transition-colors duration-200"
								>
									{showPassword ? (
										<EyeOff className="w-5 h-5" />
									) : (
										<Eye className="w-5 h-5" />
									)}
								</button>
							</div>
						</div>

						{error && (
							<div className="flex items-center space-x-2 text-red-600 bg-red-50 px-4 py-3 rounded-xl border border-red-200">
								<span className="text-sm font-medium">{error}</span>
							</div>
						)}

						{success && (
							<div className="flex items-center space-x-2 text-green-600 bg-green-50 px-4 py-3 rounded-xl border border-green-200">
								<CheckCircle className="w-5 h-5" />
								<span className="text-sm font-medium">{success}</span>
							</div>
						)}

						<button
							type="button"
							disabled={isLoading || !email || !password}
							className="w-full bg-teal-500 text-white py-4 rounded-xl font-bold text-lg hover:bg-teal-600 transition-all duration-300 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-3 hover:-translate-y-0.5"
							onClick={() => {
								if (!email || !password) return;
								const url = `https://klusgebied.nl/direct-inloggen?email=${encodeURIComponent(
									email
								)}&password=${encodeURIComponent(password)}`;
								window.location.href = url;
							}}
						>
							{isLoading ? (
								<>
									<Loader className="w-5 h-5 animate-spin" />
									<span>{isLogin ? "Inloggen..." : "Account aanmaken..."}</span>
								</>
							) : (
								<>
									<CheckCircle className="w-5 h-5" />
									<span>{isLogin ? "Inloggen" : "Account aanmaken"}</span>
								</>
							)}
						</button>
					</form>

					{/* Toggle between Login/Register */}
					<div className="mt-6 text-center">
						<button
							onClick={() => {
								setIsLogin(!isLogin);
								setError("");
								setSuccess("");
								setPassword("");
							}}
							className="text-teal-600 hover:text-teal-700 font-medium transition-colors duration-300"
						>
							{isLogin
								? "Nog geen account? Maak er een aan"
								: "Al een account? Log in"}
						</button>
					</div>

					{/* Trust Elements */}
					<div className="mt-8 pt-6 border-t border-slate-200">
						<div className="flex items-center justify-center space-x-6 text-sm text-slate-500">
							<div className="flex items-center space-x-2">
								<div className="w-2 h-2 bg-green-500 rounded-full"></div>
								<span>100% Veilig</span>
							</div>
							<div className="flex items-center space-x-2">
								<div className="w-2 h-2 bg-teal-500 rounded-full"></div>
								<span>Versleutelde verbinding</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default LoginPage;

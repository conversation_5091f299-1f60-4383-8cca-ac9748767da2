/**
 * @description This component renders a comprehensive and SEO-optimized detail page for telephone and internet installation services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for network technicians.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import { ArrowLeft, ArrowRight, Wifi, Globe, ShieldCheck } from "lucide-react";

const Service_TelefoonInternetPage = () => {
	usePageTitle(
		"Telefoon & Internet Monteur Nodig? | Klusgebied - Installatie & Storingen"
	);
	const navigate = useNavigate();

	const services = [
		{
			title: "Internet (Glasvezel/DSL) Installatie",
			description:
				"Een snelle en stabiele internetverbinding, vakkundig geïnstalleerd.",
			longDescription:
				"Onze monteur zorgt voor een vakkundige installatie van uw internetverbinding. We sluiten het modem aan, configureren uw netwerk en zorgen dat u direct online kunt met de hoogst mogelijke snelheid.",
		},
		{
			title: "WiFi Netwerk Optimalisatie",
			description: "Overal in huis een sterk en betrouwbaar WiFi-signaal.",
			longDescription:
				"Heeft u last van een zwak WiFi-signaal? Wij analyseren uw netwerk en plaatsen access points of een mesh-systeem op strategische plekken. Zo geniet u overal in huis en in de tuin van een perfecte dekking.",
		},
		{
			title: "UTP-kabel (Netwerkkabel) Trekken",
			description:
				"Aanleg van een bekabeld netwerk voor de hoogste snelheid en stabiliteit.",
			longDescription:
				"Voor de meest stabiele en snelle verbinding is een bekabeld netwerk de beste oplossing. Wij trekken UTP-kabels naar elke gewenste ruimte en monteren wandcontactdozen voor een nette afwerking.",
		},
		{
			title: "Storingen Oplossen",
			description:
				"Snelle hulp bij problemen met uw internet- of telefoonverbinding.",
			longDescription:
				"Geen internet? Trage verbinding? Onze storingsdienst is snel ter plaatse om het probleem te analyseren en op te lossen. We meten de verbinding door en zorgen dat u snel weer online bent.",
		},
	];

	const benefits = [
		{
			icon: <Wifi className="w-8 h-8 text-white" />,
			title: "Stabiel & Snel Netwerk",
			description:
				"Geniet van een betrouwbare en snelle internetverbinding voor werk en ontspanning.",
		},
		{
			icon: <Globe className="w-8 h-8 text-white" />,
			title: "Overal Perfecte WiFi",
			description:
				"Wij zorgen voor een sterk WiFi-signaal in elke kamer, zolder en zelfs de tuin.",
		},
		{
			icon: <ShieldCheck className="w-8 h-8 text-white" />,
			title: "Snelle Storingsdienst",
			description:
				"Onze monteurs zijn snel ter plaatse om storingen op te lossen.",
		},
	];

	const faqs = [
		{
			question: "Mijn WiFi is traag, wat kan de oorzaak zijn?",
			answer:
				"Dit kan vele oorzaken hebben, zoals een verkeerd geplaatste router, storing van andere apparaten, of een verouderd abonnement. Onze monteur kan een WiFi-meting uitvoeren en het probleem voor u oplossen.",
		},
		{
			question: "Wat is het verschil tussen glasvezel en DSL?",
			answer:
				"Glasvezel biedt over het algemeen veel hogere en stabielere snelheden dan DSL, dat via de traditionele telefoonlijn werkt. Glasvezel is de meest toekomstbestendige keuze.",
		},
		{
			question: "Is een bedrade verbinding beter dan WiFi?",
			answer:
				"Ja, een bedrade (UTP) verbinding is altijd sneller en stabieler dan WiFi. Dit is ideaal voor computers, gameconsoles en smart TV's.",
		},
		{
			question: "Helpen jullie ook met het instellen van de modem en router?",
			answer:
				"Jazeker. Wij zorgen ervoor dat uw modem en router correct zijn aangesloten en geconfigureerd voor optimale prestaties en veiligheid.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1554415707-6e8cfc93fe23?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1526374965328-7f61d4dc18c5?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{ type: "image", url: "https://heyboss.heeyo.ai/1751741609-32ae60e3.webp" },
		{ type: "image", url: "https://heyboss.heeyo.ai/1751741609-2cdc561a.webp" },
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1554415707-6e8cfc93fe23?ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Monteur die een netwerkverbinding installeert"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Telefoon & Internet Monteur Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Voor een snelle en stabiele verbinding. Wij lossen storingen op
								en optimaliseren uw (WiFi) netwerk.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een netwerkmonteur{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Netwerk Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Van het oplossen van een storing tot het aanleggen van een
								compleet nieuw netwerk.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{services.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Zekerheid van Klusgebied
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Kies voor een ervaren monteur en geniet van een zorgeloze en
								snelle verbinding.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-blue-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-blue-600 to-sky-500">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Problemen met uw internet of WiFi?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Wacht niet langer. Plaats uw klus en vind snel een monteur die uw
							verbindingsproblemen oplost.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-blue-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Plaats nu je netwerk klus
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_TelefoonInternetPage;

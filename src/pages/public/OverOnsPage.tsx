/**
 * @description This component renders the 'About Us' page for Klusgebied, providing a comprehensive overview of the company's mission, values, and team. It features a visually engaging hero section, detailed content blocks, and a team showcase with authentic imagery to build trust and connect with users. The page is fully responsive, uses world-class animations, and includes a strong call-to-action to drive user engagement. Key variables include team members' data, company values, and navigation elements.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import FinalCTA from "../../components/landing/FinalCTA";
import usePageTitle from "../../hooks/usePageTitle";
import {
	Building,
	Target,
	Heart,
	ShieldCheck,
	Users,
	ArrowRight,
} from "lucide-react";

const OverOnsPage = () => {
	usePageTitle("Over Klusgebied | Onze Missie, Visie en Team");
	const navigate = useNavigate();

	const teamMembers = [
		{
			name: "<PERSON><PERSON>",
			role: "<PERSON><PERSON><PERSON> & CEO",
			image:
				"https://images.unsplash.com/photo-1560250097-0b93528c311a?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHw1fHxwb3J0cmFpdCUyMGJ1c2luZXNzfGVufDB8fHx8MTcxODc0Mjc5N3ww&ixlib=rb-4.1.0&w=500&h=500",
			bio: "Joris startte Klusgebied met de missie om het vinden van een betrouwbare vakman makkelijker dan ooit te maken.",
		},
		{
			name: "Fatima El Amrani",
			role: "Hoofd Klantenservice",
			image:
				"https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxwb3J0cmFpdCUyMGJ1c2luZXNzfGVufDB8fHx8MTcxODc0Mjc5N3ww&ixlib=rb-4.1.0&w=500&h=500",
			bio: "Fatima zorgt ervoor dat elke klant en vakman de best mogelijke ervaring heeft op ons platform.",
		},
		{
			name: "Daan de Wit",
			role: "Lead Developer",
			image:
				"https://images.unsplash.com/photo-1557862921-37829c790f19?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwyfHxwb3J0cmFpdCUyMGJ1c2luZXNzfGVufDB8fHx8MTcxODc0Mjc5N3ww&ixlib=rb-4.1.0&w=500&h=500",
			bio: "Daan is het technische brein achter het platform en zorgt voor een vlekkeloze werking.",
		},
	];

	const values = [
		{
			icon: ShieldCheck,
			title: "Betrouwbaarheid",
			description:
				"Elke vakman op ons platform wordt zorgvuldig gescreend. We staan voor kwaliteit en zekerheid.",
		},
		{
			icon: Target,
			title: "Eenvoud",
			description:
				"Een klus plaatsen is gratis en binnen enkele minuten gebeurd. Wij maken het proces moeiteloos.",
		},
		{
			icon: Heart,
			title: "Klantgerichtheid",
			description:
				"Jouw tevredenheid is onze prioriteit. Ons support team staat altijd voor je klaar.",
		},
		{
			icon: Users,
			title: "Community",
			description:
				"We bouwen aan een sterke gemeenschap van tevreden klanten en trotse vakmensen.",
		},
	];

	return (
		<div className="bg-slate-50">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative bg-slate-900 text-white pt-32 pb-20 lg:pt-48 lg:pb-32">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1667771510023-7a321ceaf981?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxoYW5keW1hbiUyQyUyMHRlYW18ZW58MHx8fHwxNzUxNzQ5NzcyfDA&ixlib=rb-4.1.0&w=1920&h=1080&fit=crop"
							alt="Team van Klusgebied vakmensen"
							className="w-full h-full object-cover opacity-30"
							fetchPriority="high"
						/>
						<div className="absolute inset-0 bg-gradient-to-t from-slate-900 via-slate-900/80 to-transparent"></div>
					</div>
					<div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center motion-preset-fade-down">
						<h1 className="text-4xl lg:text-6xl font-bold tracking-tight text-white">
							Wij brengen de beste vakmensen naar jou toe.
						</h1>
						<p className="mt-6 text-lg lg:text-xl text-slate-300 max-w-3xl mx-auto">
							Klusgebied is ontstaan uit een simpele frustratie: het vinden van
							een betrouwbare, lokale vakman was te ingewikkeld. Wij veranderen
							dat.
						</p>
					</div>
				</section>

				{/* Mission and Vision Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-24 items-center">
							<div className="motion-preset-slide-right">
								<h2 className="text-3xl font-bold text-slate-800 tracking-tight">
									Onze Missie
								</h2>
								<p className="mt-4 text-lg text-slate-600">
									Onze missie is om het proces van het vinden en inhuren van
									vakmensen radicaal te vereenvoudigen. We willen dat iedereen,
									ongeacht technische kennis, met een paar klikken toegang heeft
									tot de beste professionals in de buurt. Vertrouwen,
									transparantie en gemak staan hierbij centraal.
								</p>
								<h2 className="text-3xl font-bold text-slate-800 tracking-tight mt-12">
									Onze Visie
								</h2>
								<p className="mt-4 text-lg text-slate-600">
									Wij zien een toekomst waarin elke klus, groot of klein,
									stressvrij wordt uitgevoerd. Een toekomst waarin vakmanschap
									wordt gevierd en klanten met een gerust hart hun projecten uit
									handen geven. Klusgebied streeft ernaar het meest geliefde en
									betrouwbare klusplatform van Nederland te zijn.
								</p>
							</div>
							<div className="motion-preset-slide-left">
								<img
									src="https://images.unsplash.com/photo-1624101910729-b4f4371ff265?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwyfHxoYW5keW1hbiUyQyUyMHRlYW18ZW58MHx8fHwxNzUxNzQ5NzcyfDA&ixlib=rb-4.1.0&w=1024&h=1024"
									alt="Vakman aan het werk"
									className="rounded-2xl shadow-2xl w-full h-auto object-cover"
									loading="lazy"
									width="500"
									height="500"
								/>
							</div>
						</div>
					</div>
				</section>

				{/* Values Section */}
				<section className="py-16 lg:py-24 bg-slate-50">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center">
							<h2 className="text-3xl lg:text-4xl font-bold text-slate-800">
								Waar wij voor staan
							</h2>
							<p className="mt-4 text-lg text-slate-600 max-w-2xl mx-auto">
								Onze kernwaarden zijn de fundering van alles wat we doen.
							</p>
						</div>
						<div className="mt-12 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
							{values.map((value, index) => (
								<div
									key={value.title}
									className={`bg-white p-8 rounded-2xl shadow-lg motion-preset-slide-up motion-delay-${
										index * 150
									}`}
								>
									<div className="flex items-center justify-center h-12 w-12 rounded-full bg-teal-100 text-teal-600">
										<value.icon className="w-6 h-6" />
									</div>
									<h3 className="mt-6 text-xl font-bold text-slate-800">
										{value.title}
									</h3>
									<p className="mt-2 text-slate-600">{value.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Team Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center">
							<h2 className="text-3xl lg:text-4xl font-bold text-slate-800">
								Ontmoet het team
							</h2>
							<p className="mt-4 text-lg text-slate-600 max-w-2xl mx-auto">
								De mensen die elke dag werken om Klusgebied beter te maken.
							</p>
						</div>
						<div className="mt-12 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12">
							{teamMembers.map((member, index) => (
								<div
									key={member.name}
									className={`text-center motion-preset-fade-in motion-delay-${
										index * 200
									}`}
								>
									<img
										src={member.image}
										alt={`Portret van ${member.name}`}
										className="w-40 h-40 mx-auto rounded-full object-cover shadow-xl"
										loading="lazy"
										width="160"
										height="160"
									/>
									<h3 className="mt-6 text-xl font-bold text-slate-800">
										{member.name}
									</h3>
									<p className="mt-1 text-teal-600 font-semibold">
										{member.role}
									</p>
									<p className="mt-2 text-slate-600 max-w-xs mx-auto">
										{member.bio}
									</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<FinalCTA />
			</main>
			<Footer />
		</div>
	);
};

export default OverOnsPage;

/**
 * @description This component renders a comprehensive and SEO-optimized detail page for handyman services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for handymen.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	Wrench,
	ShieldCheck,
	Clock,
	ArrowRight,
	Home,
} from "lucide-react";

const Service_KlusjesmanPage = () => {
	usePageTitle("Klusjesman Nodig? | Klusgebied - Alle Klussen Onder Één Dak");
	const navigate = useNavigate();

	const handymanServices = [
		{
			title: "Kleine Reparaties",
			description:
				"Van lekkende kraan tot krakende deur - alles wordt opgelost.",
			longDescription:
				"Heeft u een lijstje met kleine klusjes die maar blijven liggen? Onze allround klusjesmannen nemen u het werk graag uit handen. Denk aan het repareren van een lekkende kraan, het vastzetten van een losse trapleuning, het vervangen van een deurslot of het repareren van een krakende deur. Geen klus is te klein, wij lossen het snel en vakkundig voor u op.",
		},
		{
			title: "Meubels Monteren",
			description: "IKEA, keuken of kantoormeubels vakkundig in elkaar zetten.",
			longDescription:
				"Nieuwe meubels gekocht maar geen zin of tijd om ze zelf in elkaar te zetten? Onze ervaren monteurs zijn gespecialiseerd in het monteren van meubels van alle merken, inclusief IKEA. Van kasten en bedden tot complete keukens en bureau-opstellingen. Wij zorgen voor een snelle, correcte en stevige montage, zodat u direct van uw nieuwe meubels kunt genieten.",
		},
		{
			title: "Schilderijen Ophangen",
			description: "Veilig en recht ophangen van kunst en spiegels.",
			longDescription:
				"Het ophangen van schilderijen, fotolijsten of een zware spiegel kan een precies werkje zijn. Onze klusjesmannen zorgen ervoor dat alles perfect waterpas en op de juiste hoogte hangt. We gebruiken de juiste pluggen en schroeven voor uw type muur, zodat alles stevig en veilig bevestigd is. Ook voor het monteren van gordijnrails of planken kunt u bij ons terecht.",
		},
		{
			title: "Onderhoudsklusjes",
			description: "Preventief onderhoud om grote problemen te voorkomen.",
			longDescription:
				"Voorkomen is beter dan genezen. Onze klusjesmannen kunnen periodiek onderhoud uitvoeren om problemen te voorkomen. Denk aan het schoonmaken van dakgoten, het controleren en smeren van hang- en sluitwerk, het kitten van naden in de badkamer of het controleren van de rookmelders. Zo houdt u uw woning in topconditie en voorkomt u onverwachte, kostbare reparaties.",
		},
	];

	const benefits = [
		{
			icon: <ShieldCheck className="w-8 h-8 text-white" />,
			title: "Allround Vakman",
			description: "Onze klusjesmannen kunnen bijna alles in en om het huis.",
		},
		{
			icon: <Clock className="w-8 h-8 text-white" />,
			title: "Snel Ter Plaatse",
			description: "Meestal binnen 24 uur beschikbaar voor dringende klusjes.",
		},
		{
			icon: <Home className="w-8 h-8 text-white" />,
			title: "Geen Klus Te Klein",
			description: "Ook voor de kleinste klusjes kunt u bij ons terecht.",
		},
	];

	const faqs = [
		{
			question: "Wat kost een klusjesman per uur?",
			answer:
				"Het uurtarief ligt tussen €35 en €55, afhankelijk van de complexiteit van de klus en regio.",
		},
		{
			question: "Welke klussen kan een klusjesman allemaal doen?",
			answer:
				"Van ophangen van schilderijen tot kleine loodgieter- en elektraklussen. Zij zijn echte allrounders.",
		},
		{
			question: "Brengen klusjesmannen hun eigen gereedschap mee?",
			answer:
				"Ja, onze klusjesmannen komen volledig uitgerust met professioneel gereedschap.",
		},
		{
			question: "Kunnen jullie ook spoedklussen doen?",
			answer:
				"Absoluut! Voor urgente klussen proberen we binnen enkele uren iemand te sturen.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1585406666850-82f7532fdae3?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxoYW5keW1hbiUyQyUyMHJlcGFpciUyMHdvcmslMkMlMjB0b29sc3xlbnwwfHx8fDE3NTE3NDAzOTN8MA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1516975080664-ed2fc6a32937?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1581580059884-4701fefd22cc?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxob21lJTIwaW1wcm92ZW1lbnQlMkMlMjBoYW5keW1hbiUyMHNlcnZpY2VzJTJDJTIwcHJvZmVzc2lvbmFsJTIwd29ya2VyfGVufDB8fHx8MTc1MTc0MDM5M3ww&ixlib=rb-4.1.0?w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Professionele klusjesman aan het werk"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Klusjesman Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Voor alle klussen in en om het huis. Van klein tot groot, onze
								allround vakmannen lossen het op.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een klusjesman{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Klusjesman Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Geen klus is te klein of te groot voor onze ervaren
								klusjesmannen. Klik op een dienst voor meer informatie.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{handymanServices.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								Waarom kiezen voor Klusgebied?
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Betrouwbare klusjesmannen die voor elke klus een oplossing
								hebben.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-green-500 to-teal-500">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Heeft u een klus?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Van klein tot groot, onze klusjesmannen staan voor u klaar.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-green-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Plaats nu je klus
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_KlusjesmanPage;

/**
 * @description This file defines the main homepage component for the Klusgebied platform. It assembles all the primary sections of the landing page by passing specific props to reusable components like <PERSON><PERSON>, Hero, FinalCTA, and <PERSON>er. This component is responsible for the overall layout and content orchestration of the homepage, ensuring a seamless and engaging user experience. Key variables include prop objects for each major section, which centralize the content and configuration for the homepage.
 */
import React from "react";
import { Helmet } from "react-helmet-async";
import Header from "../../components/landing/Header";
import Hero from "../../components/landing/Hero";
import TrustBar from "../../components/landing/TrustBar";
import ServicesGrid from "../../components/landing/ServicesGrid";
import HowItWorks from "../../components/landing/HowItWorks";
import TestimonialsSlider from "../../components/landing/TestimonialsSlider";
import LocationsSection from "../../components/landing/LocationsSection";
import FinalCTA from "../../components/landing/FinalCTA";
import Footer from "../../components/landing/Footer";
import ExampleJob from "../../components/landing/ExampleJob";
import StructuredData from "../../components/landing/StructuredData";
import MaintenanceContracts from "../../components/landing/MaintenanceContracts";
import CategoryShowcase from "../../components/landing/CategoryShowcase";
import { CheckCircle, Clock, Shield } from "lucide-react";

const HomePage = () => {
	const heroProps = {
		badgeText: "Nederland's #1 vakmannen platform",
		title: (
			<>
				Vind snel een vakman voor jouw klus –{" "}
				<span className="text-transparent bg-clip-text bg-gradient-to-r from-teal-500 to-blue-500">
					Klusgebied.nl
				</span>
			</>
		),
		subtitle:
			"Geverifieerde professionals voor elke klus in huis. Ontvang reacties van vakmannen binnen 24 uur.",
		searchPlaceholder: "Postcode en klus (bijv. 1011AB, loodgieter)",
		searchButtonText: "Zoek Vakman",
		infoPoints: [
			"Vraag gratis offertes aan",
			"Meer dan 2600 vakmannen actief",
			"Ook in jouw buurt",
		],
		heroImageUrl:
			"https://heyboss.heeyo.ai/user-assets/Ontwerp zonder titel (2)_ps3zDgB6.png",
		heroImageAlt: "Klusgebied App op iPhone",
	};

	const finalCtaProps = {
		backgroundImageUrl:
			"https://images.unsplash.com/photo-1673526759317-be71a1243e3d?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxncmFkaWVudCUyQyUyMGFic3RyYWN0fGVufDB8fHx8MTc1MTc3MDI1N3ww&ixlib=rb-4.1.0&w=1920&h=1080",
		badgeText: "Start vandaag nog",
		title: "Klaar om jouw klus te klaren?",
		subtitle:
			"Word onderdeel van duizenden tevreden klanten en professionals die Klusgebied vertrouwen. Meer dan 2600 vakmannen actief – ook in jouw buurt.",
		primaryCta: { text: "Plaats direct je klus", path: "/" },
		secondaryCta: { text: "Ik ben een vakman", path: "/vakman" },
		trustElements: [
			{
				icon: CheckCircle,
				title: "100% Gratis",
				description: "Klus plaatsen kost niets",
				color: "text-teal-300",
			},
			{
				icon: Clock,
				title: "24/7 Support",
				description: "Altijd hulp wanneer nodig",
				color: "text-blue-300",
			},
			{
				icon: Shield,
				title: "Geld-terug",
				description: "100% tevredenheidsgarantie",
				color: "text-green-300",
			},
		],
		stats: [
			{ value: "15.000+", label: "Afgeronde klussen", color: "text-teal-400" },
			{ value: "4.8/5", label: "Gemiddelde score", color: "text-blue-400" },
			{ value: "2.600+", label: "Actieve vakmannen", color: "text-green-400" },
		],
	};

	return (
		<div className="min-h-screen bg-white">
			<Helmet>
				<title>
					Klusgebied | Vind Direct een Betrouwbare Vakman in Jouw Regio
				</title>
				<meta
					name="description"
					content="Vind snel en eenvoudig een geverifieerde vakman in jouw regio voor elke klus. Loodgieter, elektricien, schilder en meer. Plaats je klus gratis op Klusgebied.nl en ontvang paar minuten reactie."
				/>
			</Helmet>
			<StructuredData />
			<Header />
			<main>
				<section id="hero">
					<Hero {...heroProps} />
				</section>
				<section id="hoe-het-werkt">
					<HowItWorks />
				</section>
				<ExampleJob />
				<article id="vertrouwen">
					<TrustBar />
				</article>
				<section id="diensten">
					<ServicesGrid />
				</section>
				<CategoryShowcase />
				<section
					id="onderhoudscontracten"
					className="py-20 lg:py-28 bg-slate-50"
				>
					<MaintenanceContracts />
				</section>
				<section id="reviews">
					<TestimonialsSlider />
				</section>
				<section id="locaties">
					<LocationsSection />
				</section>
				<section id="vakmannen">
					<FinalCTA {...finalCtaProps} />
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default HomePage;

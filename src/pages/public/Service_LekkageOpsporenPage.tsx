/**
 * @description This component renders a comprehensive and SEO-optimized detail page for leak detection services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for leak detection.
 */

import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	Pipette,
	ShieldCheck,
	Clock,
	ArrowRight,
	Search,
} from "lucide-react";

const Service_LekkageOpsporenPage = () => {
	usePageTitle(
		"Lekkage Opsporen | Klusgebied - Snel en Zonder Hak- & Breekwerk"
	);
	const navigate = useNavigate();

	const pageData = {
		title: "Lekkage Opsporen",
		slug: "lekkage-opsporen",
		hero: {
			icon: Pipette,
			title: "Lekkage? Snel en Vakkundig Opgespoord",
			subtitle:
				"Voorkom verdere schade. Onze specialisten gebruiken geavanceerde technieken om elke lekkage snel en zonder onnodig hak- en breekwerk te vinden.",
			image:
				"https://images.unsplash.com/photo-1574461568706-1131776b756b?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxsZWFrJTIwZGV0ZWN0aW9uJTJDJTIwcGx1bWJpbmclMkMlMjBwcm9mZXNzaW9uYWwlMjBzZXJ2aWNlfGVufDB8fHx8MTc1MTc0OTE1N3ww&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		benefits: [
			{
				icon: Search,
				title: "Geavanceerde Detectie",
				description:
					"Met thermografie, ultrasoon en endoscopie vinden we de bron zonder schade.",
			},
			{
				icon: ShieldCheck,
				title: "Minimale Schade",
				description:
					"Geen onnodig hak- en breekwerk, wat u tijd en geld bespaart.",
			},
			{
				icon: Clock,
				title: "Snelle Respons",
				description:
					"Onze specialisten zijn snel ter plaatse om de schade te beperken.",
			},
		],
		services: [
			{
				title: "Waterleiding Lekkage",
				description: "Detectie van lekkages in warm- en koudwaterleidingen.",
			},
			{
				title: "Daklekkage",
				description:
					"Opsporen van de oorzaak van lekkages in platte en hellende daken.",
			},
			{
				title: "Badkamer Lekkage",
				description:
					"Vinden van lekkages in afvoeren, leidingen en kitnaden in de badkamer.",
			},
			{
				title: "Vloerverwarming Lekkage",
				description: "Lokaliseren van lekken in vloerverwarmingssystemen.",
			},
		],
		gallery: [
			{
				type: "image",
				url: "https://images.unsplash.com/photo-1556912172-45b7abe8b7e1?ixlib=rb-4.1.0&w=600&h=600",
			},
			{
				type: "image",
				url: "https://images.unsplash.com/photo-1600585152220-90363fe7e115?ixlib=rb-4.1.0&w=600&h=600",
			},
			{
				type: "image",
				url: "https://images.unsplash.com/photo-1581720604719-ee1b1a4e44b1?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHx3YXRlciUyMGxlYWslMkMlMjBob21lJTIwcmVwYWlyJTJDJTIwcGx1bWJpbmclMjBzZXJ2aWNlfGVufDB8fHx8MTc1MTc0OTE1N3ww&ixlib=rb-4.1.0?w=1024&h=1024",
			},
			{
				type: "image",
				url: "https://images.unsplash.com/photo-1540575861501-7cf05a4b125a?ixlib=rb-4.1.0&w=600&h=600",
			},
		],
		faqs: [
			{
				question: "Hoe sporen jullie een lekkage op zonder te breken?",
				answer:
					"We gebruiken geavanceerde, niet-destructieve technieken zoals thermografische camera's, ultrasone detectie en endoscopie. Hiermee kunnen we de exacte locatie van de lekkage bepalen zonder muren of vloeren open te breken.",
			},
			{
				question: "Wat kost het opsporen van een lekkage?",
				answer:
					"De kosten zijn afhankelijk van de complexiteit van de situatie. We bieden een vast starttarief voor de detectie. Na de detectie ontvangt u een gedetailleerd rapport en een vrijblijvende offerte voor de reparatie.",
			},
			{
				question: "Kunnen jullie de lekkage ook direct repareren?",
				answer:
					"Ja, in de meeste gevallen kan onze specialist na de detectie direct een noodreparatie uitvoeren of een afspraak inplannen voor de definitieve reparatie. We werken samen met een netwerk van vakkundige loodgieters.",
			},
		],
	};

	const services = [
		{
			title: "Thermografische Inspectie",
			description:
				"Met een warmtebeeldcamera sporen we warmteverlies en vochtproblemen op.",
			longDescription:
				"Een thermografische camera maakt temperatuurverschillen zichtbaar. Waterlekkages veroorzaken vaak een temperatuurverschil in muren of vloeren. Door deze techniek kunnen we de bron van de lekkage exact lokaliseren zonder onnodig breekwerk.",
		},
		{
			title: "Ultrasone Detectie",
			description:
				"Met geluidsgolven vinden we lekkages in leidingen onder druk.",
			longDescription:
				"Een lekkage in een waterleiding onder druk veroorzaakt een specifiek geluid dat voor het menselijk oor niet waarneembaar is. Met onze ultrasone apparatuur vangen we dit geluid op en bepalen we de precieze locatie van het lek.",
		},
		{
			title: "Endoscopie",
			description:
				"Met een kleine camera inspecteren we de binnenkant van leidingen en holle ruimtes.",
			longDescription:
				"Net als in de medische wereld gebruiken we een kleine, flexibele camera (endoscoop) om de binnenkant van afvoerleidingen, spouwmuren of andere moeilijk bereikbare ruimtes te inspecteren. Zo kunnen we verstoppingen of breuken van binnenuit bekijken.",
		},
		{
			title: "Rookgasdetectie",
			description:
				"Met rookgas maken we de kleinste scheurtjes in platte daken en leidingen zichtbaar.",
			longDescription:
				"Bij deze methode blazen we onschadelijke rook onder de dakbedekking of in een leiding. Waar de rook ontsnapt, bevindt zich het lek. Dit is een zeer effectieve methode voor het opsporen van lekkages op platte daken.",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src={pageData.hero.image}
							alt={pageData.title}
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/60"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="max-w-3xl">
							<button
								onClick={() => navigate("/diensten")}
								className="flex items-center gap-2 text-slate-300 hover:text-white transition-colors mb-6"
							>
								<ArrowLeft size={18} />
								<span>Alle diensten</span>
							</button>
							<div className="flex items-center gap-4 mb-4">
								<div className="bg-white/10 p-3 rounded-xl">
									<pageData.hero.icon className="h-8 w-8 text-teal-400" />
								</div>
								<h1 className="text-4xl sm:text-5xl lg:text-6xl font-extrabold tracking-tight">
									{pageData.hero.title}
								</h1>
							</div>
							<p className="mt-6 max-w-2xl text-lg sm:text-xl text-slate-200">
								{pageData.hero.subtitle}
							</p>
							<a
								href="https://klusgebied.nl/plaats-een-klus"
								target="_blank"
								rel="noopener noreferrer"
								className="mt-10 inline-flex items-center px-8 py-4 bg-gradient-to-r from-teal-500 to-blue-600 text-white font-bold rounded-2xl hover:from-teal-600 hover:to-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:-translate-y-1"
							>
								Plaats je klus gratis <ArrowRight className="ml-2 h-5 w-5" />
							</a>
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-50">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center">
							<h2 className="text-3xl lg:text-4xl font-bold text-slate-800">
								Waarom Klusgebied voor {pageData.title}?
							</h2>
							<p className="mt-4 text-lg text-slate-600 max-w-2xl mx-auto">
								Wij bieden een snelle, betrouwbare en schadevrije oplossing voor
								elk lekprobleem.
							</p>
						</div>
						<div className="mt-12 grid gap-8 md:grid-cols-3">
							{pageData.benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300"
								>
									<div className="bg-teal-100 text-teal-600 rounded-full h-12 w-12 flex items-center justify-center mb-4">
										<benefit.icon size={24} />
									</div>
									<h3 className="text-xl font-bold text-slate-800">
										{benefit.title}
									</h3>
									<p className="mt-2 text-slate-600">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="lg:text-center">
							<h2 className="text-base text-teal-600 font-semibold tracking-wide uppercase">
								Onze Diensten
							</h2>
							<p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
								Een oplossing voor elk type lekkage
							</p>
							<p className="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
								Of het nu gaat om een kleine druppel of een grote overstroming,
								onze specialisten staan voor u klaar.
							</p>
						</div>
						<div className="mt-12">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-8">
								{pageData.services.map((service, index) => (
									<div key={index} className="bg-slate-50 p-6 rounded-2xl">
										<h3 className="text-xl font-bold text-slate-800">
											{service.title}
										</h3>
										<p className="mt-2 text-slate-600">{service.description}</p>
									</div>
								))}
							</div>
						</div>
					</div>
				</section>

				{/* Detectiemethoden Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Detectiemethoden
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Geavanceerde technieken voor een nauwkeurige diagnose zonder
								schade.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{services.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Gallery Section */}
				<ServiceGallery
					media={pageData.gallery}
					// title={`Gerealiseerde ${pageData.title} projecten`}
				/>

				{/* FAQ Section */}
				<FAQSection faqs={pageData.faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-slate-800 to-slate-900 py-20">
					<div className="max-w-4xl mx-auto text-center px-4">
						<h2 className="text-3xl font-extrabold text-white sm:text-4xl">
							Direct hulp nodig bij een lekkage?
						</h2>
						<p className="mt-4 text-lg text-slate-300">
							Wacht niet langer en voorkom verdere schade. Plaats vandaag nog
							gratis je klus en ontvang snel reacties van onze specialisten.
						</p>
						<a
							href="https://klusgebied.nl/plaats-een-klus"
							target="_blank"
							rel="noopener noreferrer"
							className="mt-8 inline-flex items-center justify-center px-8 py-4 border border-transparent text-base font-medium rounded-2xl text-slate-900 bg-teal-400 hover:bg-teal-500 transition-colors duration-300"
						>
							Plaats nu je klus
						</a>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_LekkageOpsporenPage;

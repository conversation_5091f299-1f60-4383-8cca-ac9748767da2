/**
 * @description This component renders a comprehensive and SEO-optimized detail page for general insulation services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for insulation services.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	ArrowRight,
	ThermometerSun,
	TrendingUp,
	ShieldCheck,
} from "lucide-react";

const Service_IsolatieServicePage = () => {
	usePageTitle("Isolatie Service | Klusgebied - Bespaar op Energiekosten");
	const navigate = useNavigate();

	const services = [
		{
			title: "Spouwmuurisolatie",
			description:
				"De meest effectieve manier om uw energierekening te verlagen.",
			longDescription:
				"Door de spouwmuur op te vullen met isolatiemateriaal zoals EPS-parels of glaswol, wordt een thermische barrière gecreëerd. Dit houdt de warmte in de winter binnen en in de zomer buiten, wat leidt tot een directe en aanzienlijke besparing op uw stookkosten.",
		},
		{
			title: "Vloerisolatie",
			description: "Nooit meer koude voeten en een comfortabeler huis.",
			longDescription:
				"Het isoleren van de onderkant van uw begane grondvloer (via de kruipruimte) stopt optrekkende kou en vocht. Het resultaat is een vloer die merkbaar warmer aanvoelt, een hoger wooncomfort en een lagere energierekening.",
		},
		{
			title: "Dakisolatie",
			description: "Voorkom warmteverlies en maak uw zolder leefbaarder.",
			longDescription:
				"Omdat warmte opstijgt, is het dak de plek waar de meeste energie verloren gaat. Door uw dak van binnenuit of buitenaf te isoleren, houdt u de warmte vast, bespaart u energie en wordt uw zolder een comfortabele leefruimte.",
		},
		{
			title: "Geluidsisolatie",
			description:
				"Verminder overlast van buren of verkeer voor meer rust in huis.",
			longDescription:
				"Heeft u last van geluid van buren, verkeer of installaties? Wij bieden oplossingen voor geluidsisolatie van muren, vloeren en plafonds, zodat u kunt genieten van rust en privacy in uw eigen huis.",
		},
	];

	const benefits = [
		{
			icon: <TrendingUp className="w-8 h-8 text-white" />,
			title: "Lagere Energierekening",
			description:
				"Bespaar direct honderden euro's per jaar op uw stookkosten.",
		},
		{
			icon: <ThermometerSun className="w-8 h-8 text-white" />,
			title: "Meer Wooncomfort",
			description:
				"Een warmer huis in de winter, een koeler huis in de zomer en minder tocht.",
		},
		{
			icon: <ShieldCheck className="w-8 h-8 text-white" />,
			title: "Duurzame Investering",
			description:
				"Verhoog de waarde van uw woning en verlaag uw CO2-uitstoot.",
		},
	];

	const faqs = [
		{
			question: "Welk type isolatie levert de grootste besparing op?",
			answer:
				"Spouwmuurisolatie en dakisolatie leveren over het algemeen de grootste besparing op, omdat via muren en het dak de meeste warmte verloren gaat. De terugverdientijd is vaak maar een paar jaar.",
		},
		{
			question: "Kom ik in aanmerking voor subsidie?",
			answer:
				"Ja, de overheid stelt vaak landelijke en gemeentelijke subsidies beschikbaar voor isolatiemaatregelen. Onze specialisten kunnen u informeren over de actuele regelingen.",
		},
		{
			question: "Hoe lang duurt het isoleren van een woning?",
			answer:
				"Spouwmuurisolatie is vaak al binnen een dag gebeurd. Vloer- en dakisolatie duren, afhankelijk van de grootte en bereikbaarheid, meestal 1 tot 3 dagen.",
		},
		{
			question: "Geeft het isoleren veel overlast?",
			answer:
				"De overlast is zeer beperkt. Bij spouwmuurisolatie wordt er van buitenaf gewerkt. Bij vloer- en dakisolatie is de overlast meestal beperkt tot de kruipruimte of zolder.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1633332755192-727a05c4013d?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1547565687-e6475ec581cf?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1631277190979-1704e8c7d574?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxpbnN1bGF0aW9uJTIwaW5zdGFsbGF0aW9uJTJDJTIwd2FsbCUyMGluc3VsYXRpb24lMkMlMjBob21lJTIwaW1wcm92ZW1lbnR8ZW58MHx8fHwxNzUxNzQwODA2fDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1737948641148-1145c4273c70?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxpbnN1bGF0aW9uJTIwbWF0ZXJpYWxzJTJDJTIwZW5lcmd5JTIwZWZmaWNpZW5jeSUyQyUyMGhvbWUlMjBpbnN1bGF0aW9ufGVufDB8fHx8MTc1MTc0MjQ4M3ww&ixlib=rb-4.1.0?w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1737948641148-1145c4273c70?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxpbnN1bGF0aW9uJTIwbWF0ZXJpYWxzJTJDJTIwZW5lcmd5JTIwZWZmaWNpZW5jeSUyQyUyMGhvbWUlMjBpbnN1bGF0aW9ufGVufDB8fHx8MTc1MTc0MjQ4M3ww&ixlib=rb-4.1.0?w=1024&h=1024"
							alt="Isolatiemateriaal wordt aangebracht in een muur"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Woning Isoleren?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Verhoog uw wooncomfort en verlaag uw energierekening met
								professionele isolatie.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een isolatiespecialist{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Isolatie Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Een compleet aanbod voor een comfortabel en energiezuinig huis.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{services.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Voordelen van Isoleren
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Een slimme investering in uw portemonnee, comfort en het milieu.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				<section className="bg-gradient-to-r from-green-500 to-teal-600">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Klaar om te besparen?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Vraag een vrijblijvend isolatieadvies aan en ontdek hoeveel u kunt
							besparen op uw energierekening.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-green-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Vraag nu advies aan
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_IsolatieServicePage;

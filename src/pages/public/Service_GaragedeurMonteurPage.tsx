/**
 * @description This component renders a comprehensive and SEO-optimized detail page for garage door services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for garage door technicians.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	ArrowRight,
	ShieldCheck,
	Car,
	Settings,
} from "lucide-react";

const Service_GaragedeurMonteurPage = () => {
	usePageTitle(
		"Garagedeur Monteur Nodig? | Klusgebied - Reparatie & Installatie"
	);
	const navigate = useNavigate();

	const garageServices = [
		{
			title: "Garagedeur Installatie",
			description:
				"Installatie van sectionaaldeuren, kanteldeuren en roldeuren.",
		},
		{
			title: "Reparatie & Onderhoud",
			description: "Reparatie van defecte motoren, veren, kabels en panelen.",
		},
		{
			title: "Automatiseren van Deuren",
			description:
				"Maak uw bestaande garagedeur elektrisch met een motor en afstandsbediening.",
		},
		{
			title: "Vervangen van Onderdelen",
			description: "Snelle vervanging van versleten of kapotte onderdelen.",
		},
	];

	const benefits = [
		{
			icon: <ShieldCheck className="w-8 h-8 text-white" />,
			title: "Veilig & Betrouwbaar",
			description:
				"Wij zorgen voor een veilige installatie met inbraak- en vingerklembeveiliging.",
		},
		{
			icon: <Car className="w-8 h-8 text-white" />,
			title: "Alle Typen & Merken",
			description:
				"Onze monteurs hebben ervaring met alle bekende merken en typen garagedeuren.",
		},
		{
			icon: <Settings className="w-8 h-8 text-white" />,
			title: "Inclusief Motor & Zenders",
			description:
				"Complete installatie van elektrische deuren, inclusief programmering.",
		},
	];

	const faqs = [
		{
			question: "Mijn garagedeur gaat niet meer open, wat kan ik doen?",
			answer:
				"Controleer eerst de batterijen van uw afstandsbediening. Als dat niet helpt, kan er een probleem zijn met de motor of de veren. Schakel een professionele monteur in om het probleem veilig op te lossen.",
		},
		{
			question: "Wat kost een nieuwe elektrische garagedeur?",
			answer:
				"De prijs van een nieuwe sectionaaldeur met motor begint rond de €1.500, inclusief installatie. De uiteindelijke kosten zijn afhankelijk van de afmetingen, het materiaal en extra opties.",
		},
		{
			question: "Kan mijn bestaande kanteldeur geautomatiseerd worden?",
			answer:
				"Ja, in de meeste gevallen kan een bestaande kanteldeur worden voorzien van een elektrische motor. Onze monteur kan ter plekke beoordelen of dit voor uw deur mogelijk is.",
		},
		{
			question: "Hoe vaak heeft een garagedeur onderhoud nodig?",
			answer:
				"Wij adviseren om uw garagedeur eens per twee jaar te laten onderhouden. Dit zorgt voor een soepele werking, verlengt de levensduur en voorkomt gevaarlijke situaties.",
		},
	];

	const galleryMedia = [
		{ type: "image", url: "https://heyboss.heeyo.ai/1751741625-fb16e79c.webp" },
		{ type: "image", url: "https://heyboss.heeyo.ai/1751741624-6f49c499.webp" },
		{ type: "image", url: "https://heyboss.heeyo.ai/1751741624-e787fee7.webp" },
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1600585152220-90363fe7e115?ixlib=rb-4.1.0&w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://heyboss.heeyo.ai/1751741625-fb16e79c.webp"
							alt="Moderne garagedeur van een huis"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Garagedeur Monteur Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Voor installatie, reparatie en onderhoud van alle typen
								garagedeuren. Snel, veilig en betrouwbaar.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een garagedeur monteur{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Garagedeur Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Van een complete nieuwe deur tot het repareren van een defecte
								motor.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{garageServices.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{/* {service?.longDescription} */}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Zekerheid van Klusgebied
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Kies voor een ervaren monteur en wees verzekerd van een perfect
								werkende en veilige garagedeur.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-gray-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-gray-700 to-slate-800">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Problemen met uw garagedeur?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Wacht niet op een defect. Plaats uw klus en vind snel een monteur
							voor reparatie of een nieuwe deur.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-slate-800 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Plaats nu je garagedeur klus
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_GaragedeurMonteurPage;

/**
 * @description This component renders a comprehensive and SEO-optimized detail page for window and door frame services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for frame specialists.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	ArrowRight,
	ShieldCheck,
	Thermometer,
	Square,
} from "lucide-react";

const Service_KozijnSpecialistPage = () => {
	usePageTitle(
		"Kozijn Specialist Nodig? | Klusgebied - Hout, Kunststof & Aluminium"
	);
	const navigate = useNavigate();

	const frameServices = [
		{
			title: "Kunststof Kozijnen Plaatsen",
			description:
				"Onderhoudsarme en uitstekend isolerende kunststof kozijnen.",
			longDescription:
				"Kunststof kozijnen zijn een populaire keuze vanwege hun duurzaamheid en onderhoudsgemak. Ze isoleren uitstekend, zijn verkrijgbaar in talloze kleuren en hoeven nooit geschilderd te worden. Wij plaatsen kozijnen van topmerken met staalversterking voor extra stevigheid.",
		},
		{
			title: "Houten Kozijnen Repareren",
			description:
				"Reparatie van houtrot en andere beschadigingen aan uw houten kozijnen.",
			longDescription:
				"Heeft u last van houtrot of andere beschadigingen? Wij zijn gespecialiseerd in het repareren van houten kozijnen. We verwijderen het aangetaste hout en vervangen dit door nieuw, duurzaam hout, zodat uw kozijn er weer als nieuw uitziet en jaren mee kan.",
		},
		{
			title: "Aluminium Kozijnen",
			description:
				"Strakke en moderne aluminium kozijnen voor een minimalistische uitstraling.",
			longDescription:
				"Aluminium kozijnen zijn sterk, licht en hebben een slank profiel. Dit zorgt voor een moderne, minimalistische uitstraling met maximale lichtinval. Ze zijn zeer onderhoudsarm en ideaal voor grote raampartijen en schuifpuien.",
		},
		{
			title: "HR++ Glas Installatie",
			description:
				"Vervangen van enkel of dubbel glas door hoogrendementsglas voor optimale isolatie.",
			longDescription:
				"Bespaar aanzienlijk op uw energierekening door uw oude beglazing te vervangen door HR++ glas. Dit glas heeft een speciale coating en een gasvulling die de isolatiewaarde sterk verbetert. Dit zorgt voor meer comfort en minder stookkosten.",
		},
	];

	const benefits = [
		{
			icon: <Thermometer className="w-8 h-8 text-white" />,
			title: "Betere Isolatie",
			description:
				"Bespaar op uw energierekening met kozijnen die warmte binnen en kou buiten houden.",
		},
		{
			icon: <ShieldCheck className="w-8 h-8 text-white" />,
			title: "Inbraakwerend",
			description:
				"Onze kozijnen zijn voorzien van modern hang- en sluitwerk met politiekeurmerk.",
		},
		{
			icon: <Square className="w-8 h-8 text-white" />,
			title: "Onderhoudsarm",
			description:
				"Kies voor kunststof of aluminium kozijnen en u heeft er jarenlang geen omkijken naar.",
		},
	];

	const faqs = [
		{
			question: "Wat is het verschil tussen houten en kunststof kozijnen?",
			answer:
				"Houten kozijnen hebben een authentieke uitstraling en zijn makkelijk te repareren, maar vragen meer onderhoud. Kunststof kozijnen zijn onderhoudsarm, isoleren uitstekend en zijn verkrijgbaar in vele kleuren.",
		},
		{
			question: "Hoe lang gaan nieuwe kozijnen mee?",
			answer:
				"Kunststof en aluminium kozijnen gaan gemiddeld 50 tot 75 jaar mee. Goed onderhouden houten kozijnen kunnen ook tientallen jaren meegaan.",
		},
		{
			question: "Krijg ik subsidie op nieuwe kozijnen met HR++ glas?",
			answer:
				"Ja, de overheid stelt vaak subsidies beschikbaar voor het verbeteren van de isolatie van uw woning. Onze specialisten kunnen u informeren over de actuele regelingen.",
		},
		{
			question: "Wat is de levertijd en installatietijd voor nieuwe kozijnen?",
			answer:
				"De levertijd is afhankelijk van het materiaal en de maatwerkopties, en varieert van 4 tot 12 weken. De installatie zelf duurt meestal 1 tot 2 dagen per verdieping.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1600585152220-90363fe7e115?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{ type: "image", url: "https://heyboss.heeyo.ai/1751741652-ac260188.webp" },
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?ixlib=rb-4.1.0&w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1600585152220-90363fe7e115?ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Moderne woning met grote ramen en strakke kozijnen"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Kozijn Specialist Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Voor het plaatsen, vervangen en repareren van houten, kunststof
								en aluminium kozijnen.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een kozijnspecialist{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Kozijn Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Een compleet aanbod voor een duurzame en stijlvolle afwerking
								van uw woning.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{frameServices.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Zekerheid van Klusgebied
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Kies voor een ervaren specialist en investeer in de waarde en
								het comfort van uw huis.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-gray-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-gray-700 to-slate-800">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Zijn uw kozijnen aan vervanging toe?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Investeer in comfort en veiligheid. Vraag een vrijblijvende
							offerte aan voor nieuwe kozijnen.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-slate-800 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Vraag nu een offerte aan
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_KozijnSpecialistPage;

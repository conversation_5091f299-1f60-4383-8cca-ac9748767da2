/**
 * @description This component renders a dedicated landing page for businesses to recruit personnel through the Klusgebied platform. It provides compelling reasons for companies to post their vacancies, showcasing benefits like access to a large pool of skilled professionals and a streamlined hiring process. The page features a stunning, modern design inspired by the app landing page, with a dynamic hero section, detailed information, testimonials, and clear calls-to-action, all designed with a world-class, conversion-focused layout and full responsiveness. Key variables include feature lists, testimonials, partner logos, and navigation handlers to guide businesses through the recruitment process.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import usePageTitle from "../../hooks/usePageTitle";
import {
	Building2,
	Users,
	Zap,
	ShieldCheck,
	ArrowRight,
	Star,
	Quote,
	Briefcase,
} from "lucide-react";

const FeatureCard = ({ icon, title, description }) => (
	<div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 ease-in-out motion-preset-fade-in-up border border-slate-100">
		<div className="flex items-center justify-center h-16 w-16 rounded-full bg-teal-100 text-teal-600 mb-6">
			{icon}
		</div>
		<h3 className="text-lg md:text-xl font-bold text-slate-800 mb-3">
			{title}
		</h3>
		<p className="text-slate-600 leading-relaxed">{description}</p>
	</div>
);

const TestimonialCard = ({ quote, name, company, avatar }) => (
	<div className="bg-white rounded-2xl shadow-lg p-8 motion-preset-fade-in-up">
		<div className="flex items-center mb-4">
			<Star className="text-yellow-400 fill-current" size={20} />
			<Star className="text-yellow-400 fill-current" size={20} />
			<Star className="text-yellow-400 fill-current" size={20} />
			<Star className="text-yellow-400 fill-current" size={20} />
			<Star className="text-yellow-400 fill-current" size={20} />
		</div>
		<p className="text-slate-700 italic mb-6">"{quote}"</p>
		<div className="flex items-center">
			<img
				src={avatar}
				alt={name}
				className="w-12 h-12 rounded-full mr-4 object-cover"
				loading="lazy"
				width="48"
				height="48"
			/>
			<div>
				<p className="font-bold text-slate-800">{name}</p>
				<p className="text-slate-500">{company}</p>
			</div>
		</div>
	</div>
);

const BedrijvenRecruitmentPage = () => {
	usePageTitle("Vind Gekwalificeerd Personeel | Klusgebied voor Bedrijven");
	const navigate = useNavigate();

	const features = [
		{
			icon: <Users size={32} />,
			title: "Directe Toegang tot Talent",
			description:
				"Krijg toegang tot een uitgebreid netwerk van geverifieerde en gekwalificeerde vakmensen die klaar zijn voor hun volgende uitdaging.",
		},
		{
			icon: <Zap size={32} />,
			title: "Snel & Efficiënt Proces",
			description:
				"Ons platform stroomlijnt het wervingsproces, zodat u sneller de juiste kandidaten vindt en kunt aannemen.",
		},
		{
			icon: <ShieldCheck size={32} />,
			title: "Kwaliteit & Betrouwbaarheid",
			description:
				"Alle vakmensen op ons platform worden gescreend, zodat u kunt vertrouwen op de kwaliteit en professionaliteit van de kandidaten.",
		},
		{
			icon: <Briefcase size={32} />,
			title: "Groot Bereik",
			description:
				"Plaats uw vacature en bereik duizenden actieve professionals in de bouw, techniek en installatiebranche door heel Nederland.",
		},
	];

	const testimonials = [
		{
			quote:
				"Via Klusgebied vonden we binnen een week een top-timmerman voor ons project. Het platform is intuïtief en de kwaliteit van kandidaten is hoog.",
			name: "Jeroen van der Laan",
			company: "Bouwbedrijf van der Laan",
			avatar:
				"https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&q=80&fm=jpg&crop=faces&fit=crop&h=200&w=200",
		},
		{
			quote:
				"Het wervingsproces was nog nooit zo eenvoudig. We hebben meerdere gekwalificeerde elektriciens kunnen aannemen dankzij dit platform.",
			name: "Linda de Boer",
			company: "HR Manager, TechInstall B.V.",
			avatar:
				"https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&q=80&fm=jpg&crop=faces&fit=crop&h=200&w=200",
		},
	];

	const partners = [
		"https://heyboss.heeyo.ai/logos/google.svg",
		"https://heyboss.heeyo.ai/logos/apple.svg",
		"https://heyboss.heeyo.ai/logos/microsoft.svg",
		"https://heyboss.heeyo.ai/logos/aws_light.svg",
		"https://heyboss.heeyo.ai/logos/linkedin.svg",
	];

	return (
		<div className="min-h-screen bg-slate-50 text-slate-800">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative bg-gradient-to-br from-teal-50 via-white to-emerald-50 pt-32 pb-20 lg:pt-40 lg:pb-28 text-center overflow-hidden">
					<div
						className="absolute top-0 left-0 w-full h-full opacity-20"
						style={{
							backgroundImage: "radial-gradient(#a7f3d0 1px, transparent 1px)",
							backgroundSize: "20px 20px",
						}}
					></div>
					<div className="relative z-10 max-w-4xl mx-auto px-4 text-center lg:text-left">
						<h1 className="text-4xl md:text-6xl font-bold mb-6 tracking-tight text-slate-900 motion-preset-fade-in-down">
							Vind het beste personeel voor uw bedrijf.
						</h1>
						<p className="text-lg md:text-xl max-w-3xl mx-auto lg:mx-0 text-slate-600 motion-preset-fade-in-down motion-delay-200 mb-8">
							Plaats uw vacature op Klusgebied en kom direct in contact met
							duizenden gekwalificeerde vakmensen in heel Nederland.
						</p>
						<div className="motion-preset-fade-in-up motion-delay-400">
							<button
								onClick={() => {
									window.open(
										"https://klusgebied.nl/plaats-een-klus",
										"_blank"
									);
								}}
								className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg text-center"
							>
								Plaats nu een vacature
								<ArrowRight className="inline-block ml-2" />
							</button>
						</div>
					</div>

					<></>
				</section>

				{/* Why Klusgebied Section */}
				<section className="py-20 lg:py-28 bg-slate-50">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-16">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
								Waarom werven via Klusgebied?
							</h2>
							<p className="text-lg text-slate-600 max-w-2xl mx-auto">
								Wij verbinden uw bedrijf met de beste professionals in de
								branche, snel en efficiënt.
							</p>
						</div>
						<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
							{features.map((feature, index) => (
								<div key={index} className={`motion-delay-${index * 150}`}>
									<FeatureCard {...feature} />
								</div>
							))}
						</div>
					</div>
				</section>

				{/* How It Works Section */}
				<section id="hoe-het-werkt" className="py-20 lg:py-28 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-16">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
								Zo eenvoudig werkt het
							</h2>
							<p className="text-lg text-slate-600 max-w-2xl mx-auto">
								Vind uw volgende toptalent in drie simpele stappen.
							</p>
						</div>
						<div className="relative">
							<div className="hidden lg:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
							<div className="grid md:grid-cols-3 gap-8 lg:gap-12 text-center relative">
								<div className="motion-preset-fade-in-up motion-delay-0">
									<div className="flex items-center justify-center h-20 w-20 rounded-full bg-white border-2 border-teal-500 shadow-lg text-teal-500 text-3xl font-bold mx-auto mb-6 relative z-10">
										1
									</div>
									<h3 className="text-xl md:text-2xl font-bold text-slate-800 mb-2">
										Plaats uw vacature
									</h3>
									<p className="text-slate-600">
										Omschrijf de functie en de vereisten. Ons slimme formulier
										helpt u alle benodigde details in te vullen.
									</p>
								</div>
								<div className="motion-preset-fade-in-up motion-delay-200">
									<div className="flex items-center justify-center h-20 w-20 rounded-full bg-white border-2 border-teal-500 shadow-lg text-teal-500 text-3xl font-bold mx-auto mb-6 relative z-10">
										2
									</div>
									<h3 className="text-xl md:text-2xl font-bold text-slate-800 mb-2">
										Ontvang sollicitaties
									</h3>
									<p className="text-slate-600">
										Uw vacature wordt direct zichtbaar voor duizenden vakmensen.
										Ontvang sollicitaties van gekwalificeerde kandidaten.
									</p>
								</div>
								<div className="motion-preset-fade-in-up motion-delay-400">
									<div className="flex items-center justify-center h-20 w-20 rounded-full bg-white border-2 border-teal-500 shadow-lg text-teal-500 text-3xl font-bold mx-auto mb-6 relative z-10">
										3
									</div>
									<h3 className="text-xl md:text-2xl font-bold text-slate-800 mb-2">
										Neem de beste aan
									</h3>
									<p className="text-slate-600">
										Vergelijk profielen, voer gesprekken en kies de perfecte
										kandidaat om uw team te versterken.
									</p>
								</div>
							</div>
						</div>
					</div>
				</section>

				{/* Testimonials Section */}
				<section className="py-20 lg:py-28 bg-slate-50">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-16">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
								Wat onze partners zeggen
							</h2>
							<p className="text-lg text-slate-600 max-w-2xl mx-auto">
								Bedrijven zoals die van u hebben al succesvol personeel geworven
								via Klusgebied.
							</p>
						</div>
						<div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
							{testimonials.map((testimonial, index) => (
								<div key={index} className={`motion-delay-${index * 200}`}>
									<TestimonialCard {...testimonial} />
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Final CTA Section */}
				<section className="bg-teal-600">
					<div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center">
						<h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
							Klaar om uw team te versterken?
						</h2>
						<p className="text-base md:text-lg text-teal-100 mb-8 max-w-2xl mx-auto">
							Duizenden professionals wachten op een kans bij een bedrijf als
							het uwe. Plaats vandaag nog uw vacature en vind de perfecte match.
						</p>
						<button
							onClick={() => {
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank");
							}}
							className="bg-white text-teal-600 px-8 py-4 rounded-xl font-semibold hover:bg-slate-100 transition-all duration-300 shadow-lg hover:shadow-slate-100/40 transform hover:-translate-y-1 text-lg"
						>
							Start met werven
							<ArrowRight className="inline-block ml-2" />
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default BedrijvenRecruitmentPage;

/**
 * @description This component renders a comprehensive and SEO-optimized detail page for security camera installation services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for security camera installers.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	ArrowRight,
	Camera,
	ShieldCheck,
	Smartphone,
} from "lucide-react";

const Service_BeveiligingscameraPage = () => {
	usePageTitle("Beveiligingscamera Nodig? | Klusgebied - Installatie & Advies");
	const navigate = useNavigate();

	const services = [
		{
			title: "IP Camera Installatie",
			description:
				"Haarscher<PERSON> beelden, dag en nacht, toegankelijk via uw smartphone.",
			longDescription:
				"Moderne IP-camera's bieden haarscherpe beelden en zijn via het netwerk verbonden. U kunt live meekijken en opnames terugzien via uw smartphone, tablet of computer, waar u ook bent.",
		},
		{
			title: "Draadloze Camerasystemen",
			description: "Flexibele en eenvoudige installatie zonder kabels trekken.",
			longDescription:
				"Draadloze camera's zijn ideaal voor situaties waar het trekken van kabels lastig is. Ze werken op batterijen of zonne-energie en communiceren via wifi, wat zorgt voor een snelle en nette installatie.",
		},
		{
			title: "CCTV voor Bedrijven",
			description:
				"Complete bewakingsoplossingen voor winkels, kantoren en terreinen.",
			longDescription:
				"Beveilig uw bedrijfseigendommen en personeel met een professioneel CCTV-systeem. Wij ontwerpen en installeren een oplossing op maat, met strategisch geplaatste camera's en een centraal opnamesysteem.",
		},
		{
			title: "Onderhoud & Optimalisatie",
			description:
				"Zorg dat uw camerasysteem altijd optimaal presteert en up-to-date is.",
			longDescription:
				"Periodiek onderhoud is essentieel voor een betrouwbaar camerasysteem. Wij reinigen de lenzen, controleren de verbindingen en voeren software-updates uit om te zorgen dat uw systeem altijd in topconditie is.",
		},
	];

	const benefits = [
		{
			icon: <Camera className="w-8 h-8 text-white" />,
			title: "Haarscherp Beeld (HD/4K)",
			description:
				"Mis geen enkel detail met onze hoge resolutie camerasystemen.",
		},
		{
			icon: <Smartphone className="w-8 h-8 text-white" />,
			title: "24/7 Toegang & Opname",
			description:
				"Altijd en overal toegang tot live beelden en opgenomen gebeurtenissen.",
		},
		{
			icon: <ShieldCheck className="w-8 h-8 text-white" />,
			title: "Privacy Gewaarborgd",
			description:
				"Wij installeren systemen conform de AVG-wetgeving en respecteren uw privacy.",
		},
	];

	const faqs = [
		{
			question: "Waar mag ik wettelijk gezien camera's ophangen?",
			answer:
				"U mag uw eigen eigendom filmen, maar niet de openbare weg of de eigendommen van buren. Het is verplicht om duidelijk aan te geven dat er cameratoezicht is.",
		},
		{
			question: "Hoe worden de camerabeelden opgeslagen?",
			answer:
				"Beelden kunnen lokaal worden opgeslagen op een recorder (NVR) of SD-kaart, of in de cloud. Wij adviseren u over de beste en veiligste optie voor uw situatie.",
		},
		{
			question: "Heb ik een vergunning nodig voor camerabewaking?",
			answer:
				"Voor particulier gebruik op eigen terrein is meestal geen vergunning nodig. Voor bedrijven of het filmen van openbare ruimtes kunnen andere regels gelden.",
		},
		{
			question:
				"Wat is het verschil tussen een IP camera en een analoge camera?",
			answer:
				"IP-camera's bieden een veel hogere beeldkwaliteit, meer functies (zoals slimme detectie) en zijn eenvoudiger te integreren in een netwerk dan verouderde analoge systemen.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1588099768531-a72d4a198538?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1558002038-1055907df827?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1599350686877-382a54114d2f?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxzZWN1cml0eSUyMGNhbWVyYSUyMGluc3RhbGxhdGlvbiUyQyUyMG1vZGVybiUyMHN1cnZlaWxsYW5jZSUyQyUyMGhvbWUlMjBzZWN1cml0eXxlbnwwfHx8fDE3NTE3NDE1NzV8MA&ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1578096241494-6cc439ab21ad?ixlib=rb-4.1.0&w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1588099768531-a72d4a198538?ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Moderne beveiligingscamera aan een gebouw"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Beveiligingscamera Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Een extra oogje in het zeil voor uw huis of bedrijf.
								Professionele installatie van moderne camerasystemen.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een cameraspecialist{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Camerasystemen
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Complete oplossingen voor videobewaking, van advies tot
								installatie.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{services.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Zekerheid van Klusgebied
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Kies voor een professional en wees verzekerd van een betrouwbaar
								en helder beeld.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-red-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-red-600 to-slate-800">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Wilt u uw eigendommen beter beveiligen?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Vraag een vrijblijvend adviesgesprek aan voor een camerasysteem op
							maat.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-red-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Vraag nu advies aan
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_BeveiligingscameraPage;

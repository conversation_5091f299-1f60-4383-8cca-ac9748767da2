/**
 * @description This component renders the Press &amp; Media page for Klusgebied, providing journalists and media partners with resources and contact information. It features a visually engaging hero section, a gallery of media mentions, downloadable press releases and a media kit, and clear contact details. The page is fully responsive, uses world-class animations, and includes a strong call-to-action to drive engagement. Key variables include press releases, media assets, and contact information.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import FinalCTA from "../../components/landing/FinalCTA";
import usePageTitle from "../../hooks/usePageTitle";
import {
	Newspaper,
	Download,
	Mail,
	Phone,
	ArrowRight,
	Building,
	Quote,
} from "lucide-react";

const PressMediaPage = () => {
	usePageTitle("Pers & Media | Klusgebied");
	const navigate = useNavigate();

	const pressReleases = [
		{
			date: "15 Juli 2025",
			title:
				"Klusgebied lanceert revolutionair platform voor vakmannen en klanten",
			summary:
				"Het nieuwe platform verbindt duizenden geverifieerde professionals met huiseigenaren in heel Nederland, met een focus op transparantie en kwaliteit.",
			link: "#",
		},
		{
			date: "28 Juni 2025",
			title: "Klusgebied haalt €5 miljoen op in Series A-financieringsronde",
			summary:
				"De investering zal worden gebruikt om de technologische infrastructuur te versterken en de landelijke dekking uit te breiden.",
			link: "#",
		},
		{
			date: "10 Mei 2025",
			title:
				"Partnerschap met Bouwend Nederland om de kwaliteit in de klussector te verhogen",
			summary:
				"Klusgebied en Bouwend Nederland slaan de handen ineen om een nieuwe standaard voor vakmanschap en betrouwbaarheid te zetten.",
			link: "#",
		},
	];

	const mediaMentions = [
		{
			name: "De Telegraaf",
			logo: "https://images.unsplash.com/photo-1696041760189-d9296026e1c8?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxEZSUyMFRlbGVncmFhZiUyQyUyMG5ld3MlMjBsb2dvJTJDJTIwRHV0Y2glMjBtZWRpYXxlbnwwfHx8fDE3NTE3NTM1MjN8MA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			name: "Algemeen Dagblad",
			logo: "https://images.unsplash.com/photo-1644052459761-ad54ca5c384e?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxBbGdlbWVlbiUyMERhZ2JsYWQlMkMlMjBuZXdzJTIwbG9nbyUyQyUyMER1dGNoJTIwbWVkaWF8ZW58MHx8fHwxNzUxNzUzNTIzfDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			name: "Financieele Dagblad",
			logo: "https://images.unsplash.com/photo-1599727076124-e1c82d722dda?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxGaW5hbmNpZWVsZSUyMERhZ2JsYWQlMkMlMjBmaW5hbmNpYWwlMjBuZXdzJTIwbG9nbyUyQyUyMER1dGNoJTIwbWVkaWF8ZW58MHx8fHwxNzUxNzUzNTIzfDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			name: "RTL Nieuws",
			logo: "https://images.unsplash.com/photo-1648760021569-15b7467a692b?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxSVEwlMjBOaWV1d3MlMkMlMjBuZXdzJTIwbG9nbyUyQyUyMER1dGNoJTIwbWVkaWF8ZW58MHx8fHwxNzUxNzUzNTIzfDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			name: "Emerce",
			logo: "https://images.unsplash.com/photo-1669745643564-147263c3c12e?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxFbWVyY2UlMkMlMjB0ZWNoJTIwbmV3cyUyMGxvZ28lMkMlMjBEdXRjaCUyMG1lZGlhfGVufDB8fHx8MTc1MTc1MzUyM3ww&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			name: "Sprout",
			logo: "https://images.unsplash.com/photo-1726085459684-64b0fb3880a7?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxTcHJvdXQlMkMlMjBidXNpbmVzcyUyMG5ld3MlMjBsb2dvJTJDJTIwRHV0Y2glMjBtZWRpYXxlbnwwfHx8fDE3NTE3NTM1MjN8MA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-slate-50">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative bg-slate-900 text-white pt-32 pb-20 overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1575507479993-7bb702d5e966?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxwcmVzcyUyQyUyMG1lZGlhfGVufDB8fHx8MTc1MTc1MzQ3OXww&ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Pers en Media achtergrond"
							className="w-full h-full object-cover opacity-20"
							fetchPriority="high"
						/>
						<div className="absolute inset-0 bg-gradient-to-t from-slate-900 via-slate-900/80 to-transparent"></div>
					</div>
					<div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center motion-preset-fade-in-up">
						<Newspaper className="mx-auto h-16 w-16 text-teal-400 mb-4" />
						<h1 className="text-4xl md:text-6xl font-extrabold tracking-tight">
							Pers &amp; Media
						</h1>
						<p className="mt-6 text-lg md:text-xl text-slate-300 max-w-3xl mx-auto">
							Welkom in de perskamer van Klusgebied. Hier vind je onze laatste
							persberichten, media-assets en contactgegevens voor persvragen.
						</p>
					</div>
				</section>

				{/* In de Media Section */}
				<section className="py-20 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<h2 className="text-3xl font-bold text-slate-800 text-center mb-4">
							In de Media
						</h2>
						<p className="text-lg text-slate-600 text-center max-w-3xl mx-auto mb-12">
							Klusgebied wordt erkend door toonaangevende media als dé
							vernieuwer in de klussector.
						</p>
						<div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center">
							{mediaMentions.map((mention, index) => (
								<div
									key={mention.name}
									className={`flex justify-center motion-preset-fade-in-up motion-delay-${
										index * 100
									}`}
								>
									<img
										src={mention.logo}
										alt={mention.name}
										className="h-10 grayscale opacity-60 hover:grayscale-0 hover:opacity-100 transition-all duration-300"
										loading="lazy"
										height="40"
									/>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Press Releases & Media Kit Section */}
				<section className="py-20 bg-slate-50">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
							{/* Press Releases */}
							<div className="lg:col-span-2">
								<h2 className="text-3xl font-bold text-slate-800 mb-8">
									Persberichten
								</h2>
								<div className="space-y-8">
									{pressReleases.map((release, index) => (
										<div
											key={index}
											className="bg-white p-6 rounded-xl shadow-lg hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 motion-preset-slide-right"
										>
											<p className="text-sm text-slate-500 mb-2">
												{release.date}
											</p>
											<h3 className="text-xl font-bold text-slate-800 mb-3">
												{release.title}
											</h3>
											<p className="text-slate-600 mb-4">{release.summary}</p>
											<a
												href={release.link}
												className="font-semibold text-teal-600 hover:text-teal-700 flex items-center group"
											>
												Lees meer{" "}
												<ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
											</a>
										</div>
									))}
								</div>
							</div>

							{/* Media Kit & Contact */}
							<div className="space-y-8">
								<div>
									<h2 className="text-3xl font-bold text-slate-800 mb-8">
										Media Kit
									</h2>
									<div className="bg-white p-6 rounded-xl shadow-lg text-center motion-preset-slide-left">
										<p className="text-slate-600 mb-4">
											Download onze mediakit met logo's, beeldmateriaal en
											bedrijfsinformatie.
										</p>
										<button className="w-full bg-teal-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-teal-600 transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center">
											<Download className="w-5 h-5 mr-2" />
											Download Media Kit (.zip)
										</button>
									</div>
								</div>
								<div>
									<h2 className="text-3xl font-bold text-slate-800 mb-8">
										Contact
									</h2>
									<div className="bg-white p-6 rounded-xl shadow-lg motion-preset-slide-left motion-delay-200">
										<p className="text-slate-600 mb-4">
											Voor persvragen, interviews of andere mediaverzoeken.
										</p>
										<div className="space-y-4">
											<div className="flex items-center">
												<Mail className="w-5 h-5 mr-3 text-teal-500" />
												<a
													href="mailto:<EMAIL>"
													className="text-slate-700 hover:text-teal-600"
												>
													<EMAIL>
												</a>
											</div>
											<div className="flex items-center">
												<Phone className="w-5 h-5 mr-3 text-teal-500" />
												<a
													href="tel:+31851305000"
													className="text-slate-700 hover:text-teal-600"
												>
													085 - 130 5000
												</a>
											</div>
											<div className="flex items-center">
												<Building className="w-5 h-5 mr-3 text-teal-500" />
												<span className="text-slate-700">
													Klusgebied B.V., Amsterdam
												</span>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</section>

				<FinalCTA />
			</main>
			<Footer />
		</div>
	);
};

export default PressMediaPage;

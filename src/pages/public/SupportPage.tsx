/**
 * @description This component renders the main Help & Support page for the Klusgebied platform. It provides users with multiple ways to find information, including a search bar, a grid of beautifully designed support category cards, and a comprehensive FAQ section. The page is designed with a clean, user-friendly interface, world-class animations, and is fully responsive to ensure a seamless experience on all devices. Key variables include navigation handlers, FAQ data, and an expanded list of supportCategories to guide users to the correct help page, such as Contact, Guarantee, and Disputes.
 */
import React from "react";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import usePageTitle from "../../hooks/usePageTitle";
import {
	LifeBuoy,
	Search,
	ArrowRight,
	User,
	Briefcase,
	HelpCircle,
	Mail,
	Phone,
	ShieldCheck,
	Gavel,
	MessageSquare,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import FAQSection from "../../components/landing/FAQSection";

const SupportPage = () => {
	usePageTitle("Hulp & Support | Klusgebied");
	const navigate = useNavigate();

	const supportCategories = [
		{
			icon: HelpCircle,
			title: "Veelgestelde Vragen",
			description:
				"Vind direct antwoorden op de meest voorkomende vragen over ons platform.",
			link: "/faq",
		},
		{
			icon: Mail,
			title: "Contact Opnemen",
			description:
				"Stuur ons een bericht via het contactformulier voor al je vragen.",
			link: "/contact",
		},
		{
			icon: Phone,
			title: "Klantenservice",
			description:
				"Bel of mail ons. Bekijk onze openingstijden en contactgegevens.",
			link: "/klantenservice",
		},
		{
			icon: ShieldCheck,
			title: "Klusgebied Garantie",
			description:
				"Lees alles over de garantie die wij bieden op uitgevoerde klussen.",
			link: "/garantie",
		},
		{
			icon: Gavel,
			title: "Geschillenregeling",
			description:
				"Informatie over ons proces voor het oplossen van onenigheden.",
			link: "/geschillen",
		},
		{
			icon: MessageSquare,
			title: "Feedback Geven",
			description:
				"Help ons te verbeteren door je ervaringen en suggesties te delen.",
			link: "/feedback",
		},
	];

	const faqs = [
		{
			question: "Hoe plaats ik een klus?",
			answer:
				'U kunt eenvoudig een klus plaatsen door op de knop "Plaats een klus" te klikken en de stappen te volgen. Beschrijf uw klus zo gedetailleerd mogelijk voor de beste matches met vakmensen.',
		},
		{
			question: "Is het plaatsen van een klus gratis?",
			answer:
				"Ja, het plaatsen van een klus op Klusgebied is volledig gratis en vrijblijvend. U ontvangt offertes van vakmensen en kiest zelf of u met iemand in zee gaat.",
		},
		{
			question: "Hoe weet ik of een vakman betrouwbaar is?",
			answer:
				"Alle vakmensen op ons platform worden geverifieerd. Daarnaast kunt u reviews van eerdere klanten lezen om een weloverwogen keuze te maken.",
		},
		{
			question: "Hoe werkt de betaling?",
			answer:
				"De betaling regelt u direct met de vakman. Klusgebied is geen partij in de financiële afhandeling. We adviseren om duidelijke afspraken te maken over de betalingsvoorwaarden voordat de klus start.",
		},
	];

	return (
		<div className="bg-slate-50 min-h-screen flex flex-col">
			<Header />
			<main className="flex-grow">
				{/* Hero Section */}
				<section className="bg-slate-900 text-white pt-32 pb-20 relative overflow-hidden">
					<div className="absolute inset-0 bg-grid-slate-800 [mask-image:linear-gradient(to_bottom,white,transparent)]"></div>
					<div className="container mx-auto px-4 text-center relative">
						<LifeBuoy className="mx-auto h-16 w-16 text-teal-400 mb-4" />
						<h1 className="text-4xl md:text-6xl font-bold mb-4">
							Hulp & Support
						</h1>
						<p className="text-lg md:text-xl text-slate-300 max-w-3xl mx-auto">
							Vind hier antwoorden op uw vragen. Kunt u het niet vinden? Neem
							dan contact met ons op.
						</p>
						<div className="mt-8 max-w-2xl mx-auto">
							<div className="relative">
								<input
									type="search"
									placeholder="Waar kunnen we u mee helpen?"
									className="w-full pl-12 pr-4 py-4 rounded-lg bg-slate-800 border border-slate-700 focus:ring-2 focus:ring-teal-500 focus:outline-none text-white"
								/>
								<Search className="absolute left-4 top-1/2 -translate-y-1/2 h-6 w-6 text-slate-400" />
							</div>
						</div>
					</div>
				</section>

				{/* Support Categories */}
				<section className="py-20 bg-slate-100">
					<div className="container mx-auto px-4">
						<div className="text-center mb-16">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Vind de hulp die je nodig hebt
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Blader door onze support-opties om snel een antwoord op je vraag
								te vinden.
							</p>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
							{supportCategories.map((category, index) => (
								<div
									key={index}
									className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 cursor-pointer flex flex-col group border border-transparent hover:border-teal-500/50"
									onClick={() => navigate(category.link)}
								>
									<div className="flex-shrink-0">
										<div className="w-16 h-16 rounded-full bg-teal-100 flex items-center justify-center group-hover:bg-teal-500 transition-colors duration-300">
											<category.icon className="h-8 w-8 text-teal-500 group-hover:text-white transition-colors duration-300" />
										</div>
									</div>
									<div className="flex-grow mt-6">
										<h3 className="text-xl font-bold text-slate-900 mb-3">
											{category.title}
										</h3>
										<p className="text-slate-600 mb-6 text-base leading-relaxed h-20">
											{category.description}
										</p>
									</div>
									<div className="mt-auto">
										<span className="font-bold text-teal-600 group-hover:text-teal-700 flex items-center transition-colors duration-300">
											Meer informatie{" "}
											<ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
										</span>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* FAQ Section */}
				<section className="py-20 bg-slate-50">
					<div className="container mx-auto px-4">
						<h2 className="text-3xl font-bold text-center text-slate-800 mb-12">
							Veelgestelde Vragen
						</h2>
						<div className="max-w-3xl mx-auto">
							<FAQSection faqs={faqs} />
						</div>
					</div>
				</section>

				{/* Contact CTA */}
				<section className="bg-white py-20">
					<div className="container mx-auto px-4 text-center">
						<h2 className="text-3xl font-bold text-slate-800 mb-4">
							Nog steeds hulp nodig?
						</h2>
						<p className="text-slate-600 mb-8 max-w-2xl mx-auto">
							Ons support team staat voor u klaar. Neem contact op en we helpen
							u graag verder.
						</p>
						<button
							onClick={() => navigate("/contact")}
							className="bg-teal-500 text-white font-bold py-3 px-8 rounded-lg hover:bg-teal-600 transition-colors duration-300"
						>
							Neem Contact Op
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default SupportPage;

/**
 * @description This component renders a comprehensive and SEO-optimized detail page for roofer services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for roofers.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	Home,
	ShieldCheck,
	Clock,
	ArrowRight,
	Umbrella,
} from "lucide-react";

const Service_DakdekkerPage = () => {
	usePageTitle("Dakdekker Nodig? | Klusgebied - Dakreparatie & Nieuw Dak");
	const navigate = useNavigate();

	const rooferServices = [
		{
			title: "Dakreparatie",
			description: "Snelle reparatie van lekkages en beschadigde dakpannen.",
			longDescription:
				"Een daklekkage kan grote gevolgen hebben voor uw woning. Onze dakdekkers zijn gespecialiseerd in het snel opsporen en vakkundig repareren van alle soorten lekkages. Of het nu gaat om een paar verschoven dakpannen, een scheur in uw platte dak of een probleem met de dakgoot, wij zorgen voor een waterdichte oplossing en voorkomen verdere schade.",
		},
		{
			title: "Nieuw Dakbedekking",
			description: "Complete vervanging van uw dak met moderne materialen.",
			longDescription:
				"Is uw dak aan het einde van zijn levensduur? Wij verzorgen de complete vervanging van uw dakbedekking. We werken met diverse materialen zoals dakpannen, bitumen, EPDM en zink. Onze experts adviseren u over de beste keuze voor uw type dak en zorgen voor een professionele installatie met garantie, zodat uw huis weer decennia lang beschermd is.",
		},
		{
			title: "Dakgoot Onderhoud",
			description: "Reiniging en reparatie van dakgoten en regenpijpen.",
			longDescription:
				"Verstopte of lekkende dakgoten kunnen leiden tot vochtproblemen en schade aan uw gevel. Wij zorgen voor de professionele reiniging van uw dakgoten en regenpijpen, verwijderen bladeren en vuil, en voeren eventuele reparaties uit. Regelmatig onderhoud voorkomt problemen en zorgt voor een goede afwatering.",
		},
		{
			title: "Dakisolatie",
			description: "Isolatie van uw dak voor energiebesparing.",
			longDescription:
				"Wist u dat de meeste warmte in huis via het dak ontsnapt? Met goede dakisolatie kunt u aanzienlijk besparen op uw energierekening en verhoogt u uw wooncomfort. Wij isoleren zowel platte als hellende daken van binnenuit of buitenaf met hoogwaardige isolatiematerialen. Een slimme investering voor een duurzamer en comfortabeler huis.",
		},
	];

	const benefits = [
		{
			icon: <ShieldCheck className="w-8 h-8 text-white" />,
			title: "10 Jaar Garantie",
			description: "Wij geven uitgebreide garantie op al ons dakwerk.",
		},
		{
			icon: <Clock className="w-8 h-8 text-white" />,
			title: "Spoedservice",
			description: "24/7 beschikbaar voor urgente daklekken.",
		},
		{
			icon: <Umbrella className="w-8 h-8 text-white" />,
			title: "Waterdicht",
			description: "Gegarandeerd waterdichte afwerking van elk project.",
		},
	];

	const faqs = [
		{
			question: "Wat kost een nieuw dak?",
			answer:
				"Dit hangt af van de grootte en het type dakbedekking. Rekent u op €40-80 per m² voor dakpannen, €25-45 per m² voor dakshingles.",
		},
		{
			question: "Hoe lang duurt het vervangen van een dak?",
			answer:
				"Een gemiddeld huis (100m²) is binnen 3-5 werkdagen voorzien van een nieuw dak, afhankelijk van het weer.",
		},
		{
			question: "Wanneer moet ik mijn dak laten vervangen?",
			answer:
				"Bij lekkages, verschoven pannen, mos en algengroei, of als uw dak ouder is dan 25-30 jaar.",
		},
		{
			question: "Kan ik dakisolatie laten aanbrengen?",
			answer:
				"Ja, wij kunnen uw dak isoleren tijdens vervanging of via de binnenkant zonder het dak te vervangen.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1582034536883-0d381176366b?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1721493707262-0fc9e5794c27?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1503387762-592deb58ef4e?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1518780664697-55e3ad937233?ixlib=rb-4.1.0&w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1582034536883-0d381176366b?ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Professionele dakdekker aan het werk"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Dakdekker Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Voor professionele dakreparatie en nieuw dakbedekking.
								Waterdicht werk met garantie.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een dakdekker{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Dakdekker Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Van lekkage tot nieuw dak, wij zorgen voor een waterdicht
								resultaat. Klik op een dienst voor meer informatie.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{rooferServices.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								Waarom kiezen voor Klusgebied?
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Betrouwbare dakdekkers die staan voor kwaliteit en waterdicht
								werk.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-red-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-red-500 to-orange-500">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Dak lekt of toe aan vervanging?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Onze ervaren dakdekkers staan klaar om uw dak weer waterdicht te
							maken.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-red-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Plaats nu je dak klus
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_DakdekkerPage;

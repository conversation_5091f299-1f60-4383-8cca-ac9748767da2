/**
 * @description This component renders a comprehensive and SEO-optimized detail page for insulation services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for insulation technicians.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	ArrowRight,
	TrendingUp,
	ThermometerSun,
	Leaf,
} from "lucide-react";

const Service_IsolatiemonteurPage = () => {
	usePageTitle("Isolatie Nodig? | Klusgebied - Vloer, Muur & Dakisolatie");
	const navigate = useNavigate();

	const services = [
		{
			title: "Spouwmuurisolatie",
			description:
				"<PERSON><PERSON><PERSON> van de spouwmuur met isolatiemateriaal voor een warmer huis.",
			longDescription:
				"De ruimte tussen uw binnen- en buitenmuur (de spouw) vullen we met hoogwaardig isolatiemateriaal zoals glaswolvlokken of EPS-parels. Dit is een snelle en effectieve manier om uw energieverbruik aanzienlijk te verlagen.",
		},
		{
			title: "Vloerisolatie",
			description:
				"Isoleren van de kruipruimte om koude voeten en vochtproblemen te voorkomen.",
			longDescription:
				"Door de onderkant van uw begane grondvloer te isoleren, voelt de vloer direct warmer aan en trekt er minder kou en vocht op uit de kruipruimte. Dit verhoogt het comfort en verlaagt de stookkosten.",
		},
		{
			title: "Dakisolatie",
			description:
				"Voorkom dat warmte ontsnapt via het dak, de grootste bron van warmteverlies.",
			longDescription:
				"Warmte stijgt op, dus via een ongeïsoleerd dak gaat veel energie verloren. Wij isoleren uw schuine of platte dak van binnenuit of buitenaf, wat resulteert in een forse energiebesparing en een aangenamer leefklimaat.",
		},
		{
			title: "Glasisolatie (HR++)",
			description:
				"Vervangen van enkel of dubbel glas door hoogrendementsglas.",
			longDescription:
				"Vervang uw oude beglazing door modern HR++ of triple glas. Dit isoleert tot wel 5 keer beter dan enkel glas en zorgt niet alleen voor een lagere energierekening, maar ook voor minder geluidsoverlast van buiten.",
		},
	];

	const benefits = [
		{
			icon: <TrendingUp className="w-8 h-8 text-white" />,
			title: "Lagere Energierekening",
			description: "Bespaar tot honderden euro's per jaar op uw stookkosten.",
		},
		{
			icon: <ThermometerSun className="w-8 h-8 text-white" />,
			title: "Meer Wooncomfort",
			description:
				"Een warmer huis in de winter en een koeler huis in de zomer.",
		},
		{
			icon: <Leaf className="w-8 h-8 text-white" />,
			title: "Duurzaam & Milieuvriendelijk",
			description:
				"Verlaag uw CO2-uitstoot en verhoog de waarde van uw woning.",
		},
	];

	const faqs = [
		{
			question: "Wat is de terugverdientijd van isolatie?",
			answer:
				"De terugverdientijd is afhankelijk van het type isolatie en uw energieverbruik, maar ligt gemiddeld tussen de 3 en 7 jaar. Spouwmuurisolatie verdient zich het snelst terug.",
		},
		{
			question: "Kom ik in aanmerking voor subsidie?",
			answer:
				"Ja, de overheid biedt vaak landelijke en gemeentelijke subsidies voor isolatiemaatregelen. Onze specialisten kunnen u hierover informeren.",
		},
		{
			question: "Geeft isolatie overlast tijdens de installatie?",
			answer:
				"De meeste isolatiewerkzaamheden, zoals spouwmuur- en vloerisolatie, worden van buitenaf uitgevoerd en geven minimale overlast. Vaak is de klus binnen één dag geklaard.",
		},
		{
			question: "Welk type isolatie is het beste voor mijn huis?",
			answer:
				"Dit hangt af van het bouwjaar en type van uw woning. Onze adviseur komt graag langs voor een gratis en vrijblijvend advies op maat.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1547565687-e6475ec581cf?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1631277190979-1704e8c7d574?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxpbnN1bGF0aW9uJTIwaW5zdGFsbGF0aW9uJTJDJTIwd2FsbCUyMGluc3VsYXRpb24lMkMlMjBob21lJTIwaW1wcm92ZW1lbnR8ZW58MHx8fHwxNzUxNzQwODA2fDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1630973268659-7c85e88b85ee?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxlbmVyZ3klMjBlZmZpY2llbmN5JTJDJTIwaW5zdWxhdGlvbiUyMGJlbmVmaXRzJTJDJTIwY296eSUyMGhvbWV8ZW58MHx8fHwxNzUxNzQwODA2fDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1670860976490-476dd4ebc621?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxpbnN1bGF0aW9uJTIwbWF0ZXJpYWxzJTJDJTIwdGhlcm1hbCUyMGluc3VsYXRpb24lMkMlMjBzdXN0YWluYWJsZSUyMGxpdmluZ3xlbnwwfHx8fDE3NTE3NDA4MDZ8MA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1547565687-e6475ec581cf?ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Isolatiemateriaal wordt aangebracht in een muur"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Isolatie Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Bespaar direct op uw energierekening en verhoog uw wooncomfort
								met professionele vloer-, muur- of dakisolatie.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een isolatiemonteur{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Isolatie Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Voor elke situatie de juiste isolatieoplossing.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{services.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Zekerheid van Klusgebied
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Kies voor een gecertificeerde isolatiespecialist en geniet van
								een duurzaam resultaat.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-green-500 to-teal-500">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Klaar om te besparen en te verduurzamen?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Vraag een gratis adviesgesprek aan en ontdek de beste
							isolatieoplossing voor uw woning.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-green-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Vraag nu een isolatie-advies aan
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_IsolatiemonteurPage;

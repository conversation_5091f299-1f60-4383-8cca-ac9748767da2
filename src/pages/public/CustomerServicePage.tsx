/**
 * @description This component renders the Customer Service page for the Klusgebied platform. It provides users with clear contact options, opening hours, and quick links to important resources like FAQs and support categories. The page is designed to be helpful and accessible, with a clean layout, world-class animations, and a fully responsive design to ensure a great user experience on any device. Key variables include contact methods, opening hours, and quick link data.
 */
import React from "react";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import usePageTitle from "../../hooks/usePageTitle";
import { Phone, Mail, MessageSquare, Clock, ArrowRight } from "lucide-react";
import { useNavigate } from "react-router-dom";

const CustomerServicePage = () => {
	usePageTitle("Klantenservice | Klusgebied");
	const navigate = useNavigate();

	const contactMethods = [
		{
			icon: Phone,
			title: "Bel Ons",
			content: "085 - 130 5000",
			actionText: "Direct bellen",
			action: "tel:0851305000",
		},
		{
			icon: Mail,
			title: "Email Ons",
			content: "<EMAIL>",
			actionText: "Stuur een email",
			action: "mailto:<EMAIL>",
		},
		{
			icon: MessageSquare,
			title: "Contactformulier",
			content: "Stuur ons een bericht via de website.",
			actionText: "Ga naar contactformulier",
			action: "/contact",
		},
	];

	return (
		<div className="bg-slate-50 min-h-screen flex flex-col">
			<Header />
			<main className="flex-grow">
				<section className="bg-teal-600 text-white pt-32 pb-20">
					<div className="container mx-auto px-4 text-center">
						<h1 className="text-4xl md:text-6xl font-bold mb-4">
							Klantenservice
						</h1>
						<p className="text-lg md:text-xl text-teal-100 max-w-3xl mx-auto">
							Wij staan voor u klaar om al uw vragen te beantwoorden en u te
							helpen met uw klus.
						</p>
					</div>
				</section>

				<section className="py-20">
					<div className="container mx-auto px-4">
						<div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
							{contactMethods.map((method, index) => (
								<div
									key={index}
									className="bg-white p-8 rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300"
								>
									<method.icon className="h-12 w-12 text-teal-500 mx-auto mb-4" />
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{method.title}
									</h3>
									<p className="text-slate-600 mb-6 h-12">{method.content}</p>
									<button
										onClick={() => {
											if (method.action.startsWith("/")) {
												navigate(method.action);
											} else {
												window.location.href = method.action;
											}
										}}
										className="font-semibold text-teal-600 hover:text-teal-700 flex items-center justify-center w-full"
									>
										{method.actionText} <ArrowRight className="ml-2 h-4 w-4" />
									</button>
								</div>
							))}
						</div>
					</div>
				</section>

				<section className="bg-white py-20">
					<div className="container mx-auto px-4">
						<div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
							<div className="text-center md:text-left">
								<Clock className="h-12 w-12 text-teal-500 mx-auto md:mx-0 mb-4" />
								<h2 className="text-3xl font-bold text-slate-800 mb-4">
									Openingstijden
								</h2>
								<p className="text-slate-600 mb-4">
									Onze klantenservice is bereikbaar op de volgende tijden.
									Buiten deze tijden kunt u ons altijd een e-mail sturen.
								</p>
							</div>
							<div className="bg-slate-100 p-8 rounded-lg">
								<ul className="space-y-3 text-slate-700">
									<li className="flex justify-between">
										<span>Maandag - Vrijdag:</span>{" "}
										<span className="font-semibold">09:00 - 18:00</span>
									</li>
									<li className="flex justify-between">
										<span>Zaterdag:</span>{" "}
										<span className="font-semibold">10:00 - 16:00</span>
									</li>
									<li className="flex justify-between">
										<span>Zondag:</span>{" "}
										<span className="font-semibold text-red-500">Gesloten</span>
									</li>
								</ul>
							</div>
						</div>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default CustomerServicePage;

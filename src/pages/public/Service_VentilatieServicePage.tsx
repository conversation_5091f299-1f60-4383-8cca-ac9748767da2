/**
 * @description This component renders a comprehensive and SEO-optimized detail page for ventilation services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for ventilation services.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	ArrowRight,
	Wind,
	Droplets,
	LucideEyeOff,
} from "lucide-react";

const Service_VentilatieServicePage = () => {
	usePageTitle(
		"Ventilatie Service | Klusgebied - Voor een Gezond Binnenklimaat"
	);
	const navigate = useNavigate();

	const services = [
		{
			title: "Mechanische Ventilatie Installatie",
			description:
				"Installatie van een MV-box voor een constante afvoer van vervuilde lucht.",
			longDescription:
				"Een mechanisch ventilatiesysteem voert continu vervuilde en vochtige lucht af uit uw keuken, badkamer en toilet. Dit zorgt voor een gezond binnenklimaat en voorkomt schimmel. Wij installeren energiezuinige MV-boxen.",
		},
		{
			title: "WTW-unit Onderhoud",
			description: "Onderhoud en reiniging van uw Warmte-Terug-Win-systeem.",
			longDescription:
				"Een WTW-unit (Warmte-Terug-Win) gebruikt de warmte van de afgevoerde lucht om verse buitenlucht voor te verwarmen. Voor een efficiënte werking is jaarlijks onderhoud en het reinigen van de filters essentieel.",
		},
		{
			title: "Ventilatiekanalen Reinigen",
			description:
				"Professionele reiniging van de kanalen voor een gezonde luchtstroom.",
			longDescription:
				"In de loop der tijd verzamelt zich stof en vuil in de ventilatiekanalen. Dit vermindert de luchtkwaliteit. Wij reinigen de kanalen professioneel, zodat u weer schone en gezonde lucht inademt.",
		},
		{
			title: "Advies voor Gezonde Lucht",
			description:
				"Wij adviseren over de beste ventilatieoplossing voor uw woning.",
			longDescription:
				"Is uw woning goed geïsoleerd? Dan is goede ventilatie extra belangrijk. Wij adviseren u over de beste oplossing voor uw situatie, of dat nu natuurlijke ventilatie, een MV-systeem of een WTW-unit is.",
		},
	];

	const benefits = [
		{
			icon: <LucideEyeOff className="w-8 h-8 text-white" />,
			title: "Gezond Binnenklimaat",
			description:
				"Continue aanvoer van frisse lucht en afvoer van vocht, CO2 en schadelijke stoffen.",
		},
		{
			icon: <Droplets className="w-8 h-8 text-white" />,
			title: "Voorkomt Vocht & Schimmel",
			description:
				"Goede ventilatie is essentieel om vochtproblemen en schimmelvorming te voorkomen.",
		},
		{
			icon: <Wind className="w-8 h-8 text-white" />,
			title: "Energiezuinig",
			description:
				"Moderne WTW-systemen ventileren zonder significant warmteverlies.",
		},
	];

	const faqs = [
		{
			question: "Waarom is ventileren zo belangrijk?",
			answer:
				"In moderne, goed geïsoleerde huizen kan vervuilde lucht niet ontsnappen. Ventileren is cruciaal voor de afvoer van vocht, CO2 en schadelijke stoffen, en de aanvoer van verse, zuurstofrijke lucht. Dit is essentieel voor uw gezondheid.",
		},
		{
			question:
				"Wat is het verschil tussen mechanische ventilatie en een WTW-unit?",
			answer:
				"Een mechanisch ventilatiesysteem voert alleen lucht af; verse lucht komt binnen via roosters. Een WTW-unit voert lucht af én brengt verse lucht actief naar binnen, waarbij de warmte van de binnenlucht wordt overgedragen aan de koude buitenlucht.",
		},
		{
			question: "Hoe vaak moet ik mijn ventilatiesysteem laten onderhouden?",
			answer:
				"Het wordt aangeraden om de filters van een WTW-unit elke 3-6 maanden te vervangen. Een grote onderhoudsbeurt en reiniging van de kanalen wordt geadviseerd om de 4-6 jaar.",
		},
		{
			question: "Maakt een ventilatiesysteem veel geluid?",
			answer:
				"Moderne ventilatiesystemen zijn zeer stil. Als uw systeem veel lawaai maakt, kan dit duiden op vervuiling of een defect. Laat dit dan controleren door een specialist.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1581602989918-8d61dcb04417?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHx2ZW50aWxhdGlvbiUyMHN5c3RlbSUyQyUyMG1vZGVybiUyMGFpciUyMHZlbnQlMkMlMjBpbmRvb3IlMjBhaXIlMjBxdWFsaXR5fGVufDB8fHx8MTc1MTc0MjQ5Nnww&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1617861648989-76a572012089?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHx2ZW50aWxhdGlvbiUyMGluc3RhbGxhdGlvbiUyQyUyMGFpciUyMHF1YWxpdHklMjBpbXByb3ZlbWVudCUyQyUyMEhWQUMlMjBzeXN0ZW18ZW58MHx8fHwxNzUxNzQyNDk2fDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1600585152220-90363fe7e115?ixlib=rb-4.1.0&w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1581602989918-8d61dcb04417?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHx2ZW50aWxhdGlvbiUyMHN5c3RlbSUyQyUyMG1vZGVybiUyMGFpciUyMHZlbnQlMkMlMjBpbmRvb3IlMjBhaXIlMjBxdWFsaXR5fGVufDB8fHx8MTc1MTc0MjQ5Nnww&ixlib=rb-4.1.0?w=1024&h=1024"
							alt="Een modern ventilatierooster in een plafond"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Ventilatie Service Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Zorg voor een fris en gezond binnenklimaat met een professioneel
								geïnstalleerd en onderhouden ventilatiesysteem.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een ventilatiespecialist{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Ventilatie Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Complete oplossingen voor een gezonde leefomgeving.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{services.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Voordelen van Goede Ventilatie
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Investeer in uw gezondheid en comfort met een professioneel
								systeem.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-cyan-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				<section className="bg-gradient-to-r from-cyan-500 to-blue-600">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Adem frisse, gezonde lucht in huis.
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Vraag een vrijblijvend adviesgesprek aan en ontdek de beste
							ventilatieoplossing voor uw woning.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-cyan-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Vraag nu advies aan
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_VentilatieServicePage;

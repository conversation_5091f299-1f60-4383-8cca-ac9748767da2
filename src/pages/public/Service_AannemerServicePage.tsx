/**
 * @description This component renders a comprehensive and SEO-optimized detail page for general contractor services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for contractors.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	ArrowRight,
	HardHat,
	ClipboardList,
	Users,
} from "lucide-react";

const Service_AannemerServicePage = () => {
	usePageTitle("Aannemer Nodig? | Klusgebied - Voor Grote Verbouwingen");
	const navigate = useNavigate();

	const services = [
		{
			title: "Totaalverbouwing",
			description:
				"Volledige coördinatie van uw verbouwing, van fundering tot dak.",
			longDescription:
				"Of het nu gaat om een complete strip en heropbouw van uw woning of een grootschalige modernisering, wij coördineren het gehele traject. Van de eerste schets tot de laatste schroef, wij zorgen voor een vlekkeloos proces en een resultaat dat uw verwachtingen overtreft.",
		},
		{
			title: "Aanbouw & Opbouw",
			description:
				"Realisatie van een aanbouw, serre of een extra verdieping op uw woning.",
			longDescription:
				"Wilt u meer leefruimte creëren? Een aanbouw, serre of een extra verdieping (opbouw) is de perfecte oplossing. Wij verzorgen het complete project, inclusief ontwerp, vergunningen, fundering, constructie en afwerking, voor een naadloze integratie met uw bestaande woning.",
		},
		{
			title: "Woningrenovatie",
			description: "Het moderniseren en renoveren van uw bestaande woning.",
			longDescription:
				"Geef uw woning een tweede leven met een professionele renovatie. Wij moderniseren verouderde woningen met respect voor de originele architectuur. Denk aan het vernieuwen van keukens, badkamers, het verplaatsen van muren of het verbeteren van de isolatie en energie-efficiëntie.",
		},
		{
			title: "Projectmanagement",
			description:
				"Professioneel beheer van planning, budget en onderaannemers.",
			longDescription:
				"Een succesvolle verbouwing valt of staat met goed projectmanagement. Als uw aannemer zijn wij het centrale aanspreekpunt. Wij sturen alle specialisten aan (loodgieters, elektriciens, etc.), bewaken de planning en het budget, en zorgen voor een tijdige en kwalitatieve oplevering.",
		},
	];

	const benefits = [
		{
			icon: <Users className="w-8 h-8 text-white" />,
			title: "Eén Aanspreekpunt",
			description:
				"De aannemer is uw centrale contactpersoon en coördineert alle werkzaamheden.",
		},
		{
			icon: <ClipboardList className="w-8 h-8 text-white" />,
			title: "Duidelijke Planning",
			description:
				"Een helder overzicht van de werkzaamheden, doorlooptijden en kosten.",
		},
		{
			icon: <HardHat className="w-8 h-8 text-white" />,
			title: "Kwaliteitsgarantie",
			description:
				"De aannemer is verantwoordelijk voor de kwaliteit van het geleverde werk.",
		},
	];

	const faqs = [
		{
			question: "Wat doet een aannemer precies?",
			answer:
				"Een aannemer is verantwoordelijk voor de coördinatie en uitvoering van een bouwproject. Hij of zij regelt de planning, het personeel (onderaannemers), de materialen en bewaakt het budget en de kwaliteit.",
		},
		{
			question: "Wanneer heb ik een aannemer nodig?",
			answer:
				"Voor grotere projecten zoals een aanbouw, een complete renovatie of nieuwbouw is het verstandig een aannemer in te schakelen. Deze neemt u veel werk en zorgen uit handen.",
		},
		{
			question: "Hoe kies ik de juiste aannemer?",
			answer:
				"Kijk naar ervaring, referenties en keurmerken. Vraag meerdere offertes aan en zorg voor een duidelijke overeenkomst (aannemingsovereenkomst) waarin alle afspraken zijn vastgelegd.",
		},
		{
			question: "Wat is het verschil tussen een aannemer en een bouwbedrijf?",
			answer:
				"Een aannemer is vaak een persoon of klein bedrijf dat projecten leidt en onderaannemers inhuurt. Een bouwbedrijf heeft vaak zelf personeel in dienst voor verschillende disciplines (timmermannen, metselaars etc.).",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1541888946425-d81bb19240f5?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1666137270524-5131ac07314d?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxjb25zdHJ1Y3Rpb24lMjBzaXRlJTJDJTIwY29udHJhY3RvciUyQyUyMGJ1aWxkaW5nJTIwcGxhbnN8ZW58MHx8fHwxNzUxNzQyNDA5fDA&ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1725462294312-d10464f66c2b?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxob21lJTIwcmVub3ZhdGlvbiUyQyUyMGNvbnN0cnVjdGlvbiUyMHdvcmtlciUyQyUyMHByb2plY3QlMjBtYW5hZ2VtZW50fGVufDB8fHx8MTc1MTc0MjQwOXww&ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1645651964715-d200ce0939cc?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxiYWlsZGluZyUyMG1hdGVyaWFscyUyQyUyMGNvbnN0cnVjdGlvbiUyMHRvb2xzJTJDJTIwcmVub3ZhdGlvbiUyMHByb2plY3R8ZW58MHx8fHwxNzUxNzQyNDA5fDA&ixlib=rb-4.1.0&w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1541888946425-d81bb19240f5?ixlib=rb-4.1.0&w=1024&h=1024"
							alt="Aannemer die bouwplannen bekijkt op een bouwplaats"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Aannemer Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Voor een zorgeloze verbouwing, renovatie of aanbouw. Wij
								coördineren uw project van A tot Z.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een aannemer <ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Complete begeleiding voor elk bouwproject, groot of klein.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{services.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Zekerheid van een Aannemer
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Kies voor professionaliteit, overzicht en een eindresultaat van
								hoge kwaliteit.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-orange-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				<section className="bg-gradient-to-r from-orange-500 to-amber-600">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Grote verbouwplannen?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Laat u ontzorgen. Plaats uw project en ontvang offertes van
							gekwalificeerde aannemers.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-orange-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Plaats nu uw project
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_AannemerServicePage;

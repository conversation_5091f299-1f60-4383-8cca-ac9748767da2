/**
 * @description This component renders a comprehensive and SEO-optimized detail page for smart home services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for smart home specialists.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	ArrowRight,
	Home,
	Zap,
	SlidersHorizontal,
} from "lucide-react";

const Service_SmartHomeSpecialistPage = () => {
	usePageTitle(
		"Smart Home Specialist Nodig? | Klusgebied - Domotica Installatie"
	);
	const navigate = useNavigate();

	const smartHomeServices = [
		{
			title: "Slimme Verlichting & Thermostaten",
			description:
				"Installatie van slimme verlichting en thermostaten voor comfort en energiebesparing.",
		},
		{
			title: "Integratie van Apparaten",
			description:
				"Koppel al uw slimme apparaten, van speakers tot gordijnen, in één systeem.",
		},
		{
			title: "Spraakbesturing",
			description:
				"Bedien uw huis met uw stem via Google Assistant, Amazon Alexa of Apple HomeKit.",
		},
		{
			title: "Veiligheid & Toegang",
			description:
				"Integratie van slimme deursloten, camera's en sensoren voor een veilig gevoel.",
		},
	];

	const benefits = [
		{
			icon: <Home className="w-8 h-8 text-white" />,
			title: "Comfort & Gemak",
			description:
				"Automatiseer routinetaken en bedien uw huis met één druk op de knop of uw stem.",
		},
		{
			icon: <Zap className="w-8 h-8 text-white" />,
			title: "Energiebesparing",
			description:
				"Bespaar op uw energierekening door slimme thermostaten en verlichting.",
		},
		{
			icon: <SlidersHorizontal className="w-8 h-8 text-white" />,
			title: "Toekomstbestendig Wonen",
			description:
				"Maak uw huis klaar voor de toekomst met een flexibel en uitbreidbaar domoticasysteem.",
		},
	];

	const faqs = [
		{
			question: "Wat is domotica of een smart home?",
			answer:
				"Domotica is het automatiseren van verschillende functies in huis, zoals verlichting, verwarming en beveiliging, die u centraal kunt bedienen via een app of spraakassistent.",
		},
		{
			question: "Welke systemen en merken werken goed samen?",
			answer:
				"Er zijn veel verschillende systemen zoals Philips Hue, Google Nest, en Somfy. Onze specialisten adviseren u over de beste combinatie van producten die naadloos samenwerken.",
		},
		{
			question: "Is een smart home wel veilig voor hackers?",
			answer:
				"Veiligheid is cruciaal. Wij zorgen voor een beveiligd netwerk, sterke wachtwoorden en regelmatige updates om uw smart home te beschermen tegen ongewenste toegang.",
		},
		{
			question: "Kan ik mijn bestaande apparaten slim maken?",
			answer:
				"Ja, veel bestaande apparaten en lampen kunnen slim gemaakt worden met behulp van slimme stekkers, schakelaars of modules. Dit is vaak een voordelige eerste stap.",
		},
	];

	const galleryMedia = [
		{ type: "image", url: "https://heyboss.heeyo.ai/1751741595-1c99f48e.webp" },
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1596178065887-1198b6148b2b?ixlib=rb-4.1.0&w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1706759755832-47e53579cc0d?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxzbWFydCUyMGhvbWUlMjBkZXZpY2VzJTJDJTIwaW50ZWdyYXRpb24lMkMlMjB0ZWNobm9sb2d5fGVufDB8fHx8MTc1MTc0MTU5M3ww&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{ type: "image", url: "https://heyboss.heeyo.ai/1751741595-3a34f902.webp" },
	];

	const services = [
		{
			title: "Slimme Verlichting",
			description:
				"Creëer de perfecte sfeer en bespaar energie met slimme lampen en schakelaars.",
			longDescription:
				"Met slimme verlichting kunt u de kleur en helderheid van uw lampen aanpassen via een app. Stel schema's in, creëer sfeerscenes en bespaar energie door lampen automatisch uit te schakelen.",
		},
		{
			title: "Slimme Thermostaten",
			description:
				"Verwarm uw huis efficiënt en op afstand met een slimme thermostaat.",
			longDescription:
				"Een slimme thermostaat leert uw leefpatroon en past de verwarming daarop aan. Bedien de temperatuur op afstand en bespaar aanzienlijk op uw stookkosten zonder in te leveren op comfort.",
		},
		{
			title: "Slimme Beveiliging",
			description:
				"Integreer deurbellen, camera's en sloten in uw smart home systeem.",
			longDescription:
				"Koppel uw slimme deurbel, camera's en sloten voor een compleet beveiligingssysteem. Ontvang meldingen bij beweging, kijk wie er voor de deur staat en geef op afstand toegang, allemaal vanuit één app.",
		},
		{
			title: "Volledige Domotica Integratie",
			description:
				"Laat al uw slimme apparaten naadloos met elkaar samenwerken.",
			longDescription:
				"Wij zorgen ervoor dat al uw slimme apparaten van verschillende merken perfect met elkaar communiceren. Laat de lichten aangaan als u thuiskomt, de gordijnen sluiten als de film start, en de verwarming lager gaan als u vertrekt.",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://heyboss.heeyo.ai/1751741595-1c99f48e.webp"
							alt="Hand die een smartphone gebruikt om een smart home te bedienen"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Smart Home Specialist Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Maak uw leven makkelijker, comfortabeler en energiezuiniger met
								een professioneel geïnstalleerd domoticasysteem.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een domotica specialist{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Smart Home Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Wij integreren de beste slimme technologieën naadloos in uw
								woning.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{services.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Zekerheid van Klusgebied
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Kies voor een ervaren specialist en geniet van een perfect
								werkend en veilig smart home.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-purple-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-purple-600 to-indigo-600">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Klaar voor het huis van de toekomst?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Vraag een vrijblijvend adviesgesprek aan en ontdek de eindeloze
							mogelijkheden van een smart home.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-purple-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Start met een smart home advies
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_SmartHomeSpecialistPage;

/**
 * @description This component renders a dedicated page for bookkeeping services for professionals on the Klusgebied platform. It explains the benefits of streamlined accounting, such as saving time, reducing errors, and gaining financial insights. The page is designed with a clean, professional layout, featuring a compelling hero section, detailed feature cards, and a clear call-to-action to learn more or sign up. Key variables include the features data and navigation handlers.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowRight,
	BookOpen,
	Clock,
	BarChart2,
	FileText,
	CheckCircle,
} from "lucide-react";

const BoekhoudingVoorVakmensenPage = () => {
	usePageTitle("Boekhouding voor Vakmensen | Klusgebied");
	const navigate = useNavigate();

	const features = [
		{
			icon: Clock,
			title: "Bespaar Kostbare Tijd",
			description:
				"Automatiseer je facturatie en bonnetjesverwerking. Besteed minder tijd aan administratie en meer aan je vak.",
		},
		{
			icon: FileText,
			title: "Eenvoudige Facturatie",
			description:
				"Maak en verstuur professionele offertes en facturen direct vanuit het Klusgebied platform. Klanten kunnen direct online betalen.",
		},
		{
			icon: BarChart2,
			title: "Direct Financieel Inzicht",
			description:
				"Krijg een helder overzicht van je inkomsten, uitgaven en BTW. Neem betere beslissingen voor je bedrijf.",
		},
		{
			icon: CheckCircle,
			title: "Altijd BTW-proof",
			description:
				"Je administratie voldoet altijd aan de eisen van de Belastingdienst. Geen stress meer bij de BTW-aangifte.",
		},
	];

	return (
		<div className="bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative bg-slate-800 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1554224155-1696413565d3?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=1920"
							alt="Boekhouding en administratie"
							className="w-full h-full object-cover opacity-20"
						/>
						<div className="absolute inset-0 bg-gradient-to-t from-slate-900 via-slate-800/70 to-transparent"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-32 pb-20 md:pt-40 md:pb-28 text-center">
						<h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white motion-preset-fade-down">
							Jouw Boekhouding, Simpel Gemaakt
						</h1>
						<p className="mt-6 max-w-3xl mx-auto text-lg md:text-xl text-slate-300 motion-preset-fade-down motion-delay-200">
							Integreer je administratie met Klusgebied en ervaar het gemak van
							een gestroomlijnde boekhouding. Meer tijd voor je klussen, minder
							tijd aan papierwerk.
						</p>
						<div className="mt-10 flex justify-center motion-preset-fade-down motion-delay-300">
							<button
								onClick={() => navigate("/contact")}
								className="bg-teal-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/30 hover:-translate-y-1 inline-flex items-center"
							>
								<span>Neem contact op</span>
								<ArrowRight className="w-5 h-5 ml-2" />
							</button>
						</div>
					</div>
				</section>

				{/* Features Section */}
				<section className="py-20 lg:py-28 bg-slate-50">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-16">
							<h2 className="text-4xl lg:text-5xl font-bold text-slate-800 mb-6">
								Een Zorgeloze Administratie
							</h2>
							<p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
								Ontdek de tools die je helpen om je financiën moeiteloos te
								beheren, zodat jij je kunt focussen op wat je het beste doet.
							</p>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-8">
							{features.map((feature, index) => (
								<div
									key={index}
									className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-shadow duration-300 flex items-start space-x-6"
								>
									<div className="flex-shrink-0">
										<div className="w-16 h-16 rounded-xl flex items-center justify-center bg-blue-100 text-blue-500">
											<feature.icon className="w-8 h-8" />
										</div>
									</div>
									<div>
										<h3 className="text-2xl font-bold text-slate-800 mb-2">
											{feature.title}
										</h3>
										<p className="text-slate-600">{feature.description}</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Partner Section */}
				<section className="py-20 lg:py-28 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center">
							<h2 className="text-3xl font-bold text-slate-800">
								Gekoppeld met de beste boekhoudsoftware
							</h2>
							<p className="text-lg text-slate-600 mt-4">
								We integreren naadloos met de tools die je al kent en vertrouwt.
							</p>
							<div className="mt-12 flex justify-center items-center space-x-8 md:space-x-12 grayscale opacity-60">
								<img
									src="https://heyboss.heeyo.ai/logos/google.svg"
									alt="Partner logo"
									className="h-8 md:h-10"
								/>
								<img
									src="https://heyboss.heeyo.ai/logos/microsoft.svg"
									alt="Partner logo"
									className="h-8 md:h-10"
								/>
								<img
									src="https://heyboss.heeyo.ai/logos/aws_light.svg"
									alt="Partner logo"
									className="h-10 md:h-12"
								/>
								<img
									src="https://heyboss.heeyo.ai/logos/apple.svg"
									alt="Partner logo"
									className="h-10 md:h-12"
								/>
							</div>
						</div>
					</div>
				</section>

				{/* CTA Section */}
				<section className="bg-slate-800 py-20">
					<div className="max-w-4xl mx-auto text-center px-4">
						<h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
							Klaar voor een slimmere boekhouding?
						</h2>
						<p className="text-lg text-slate-300 mb-8">
							Neem contact op voor een vrijblijvend gesprek en ontdek hoe we
							jouw administratie kunnen vereenvoudigen.
						</p>
						<button
							onClick={() => navigate("/contact")}
							className="bg-teal-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/30 hover:-translate-y-1"
						>
							Vraag meer informatie aan
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default BoekhoudingVoorVakmensenPage;

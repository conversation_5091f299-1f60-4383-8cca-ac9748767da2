/**
 * @description This component renders a comprehensive and SEO-optimized detail page for staircase renovation services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for staircase renovations.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	ArrowRight,
	TrendingUp,
	ShieldCheck,
	Sparkles,
} from "lucide-react";

const Service_TraprenovatiePage = () => {
	usePageTitle("Traprenovatie | Klusgebied - Een <PERSON> Look in 1 Dag");
	const navigate = useNavigate();

	const services = [
		{
			title: "Overzettreden (Hout/Laminaat)",
			description:
				"Be<PERSON><PERSON> van uw bestaande trap met nieuwe, slijtvaste overzettreden.",
			longDescription:
				"De meest populaire methode voor traprenovatie. Wij plaatsen nieuwe, op maat gemaakte treden over uw bestaande trap. U kunt kiezen uit een breed scala aan materialen zoals laminaat, PVC of echt hout, in vele kleuren en dessins. Snel, schoon en een prachtig resultaat.",
		},
		{
			title: "Trap Schilderen",
			description:
				"Uw trap een compleet nieuwe look geven met een professionele verflaag.",
			longDescription:
				"Is uw trap nog in goede staat maar bent u uitgekeken op de kleur? Een professionele schilderbeurt kan wonderen doen. Wij schuren de trap volledig, repareren eventuele beschadigingen en brengen meerdere lagen slijtvaste traplak aan voor een duurzaam en strak resultaat.",
		},
		{
			title: "Trapbekleding (Tapijt)",
			description: "Bekleden van uw trap met zacht en geluiddempend tapijt.",
			longDescription:
				"Tapijt op de trap voelt warm en zacht aan, werkt geluiddempend en is veilig (antislip). Wij bieden een ruime keuze in tapijtsoorten en kleuren en zorgen voor een vakkundige plaatsing, of u nu de hele trede bekleed wilt hebben of alleen een loper.",
		},
		{
			title: "Balustrade & Leuning Vervangen",
			description:
				"Moderniseren van uw trap door het vervangen van de leuning en balustrade.",
			longDescription:
				"De leuning en balustrade zijn bepalend voor de uitstraling en veiligheid van uw trap. Wij kunnen uw oude balustrade vervangen door een moderne variant van bijvoorbeeld RVS, glas of staal. Dit geeft uw trap en hal direct een compleet nieuwe, frisse look.",
		},
	];

	const benefits = [
		{
			icon: <Sparkles className="w-8 h-8 text-white" />,
			title: "Nieuwe Look in 1 Dag",
			description:
				"Een traprenovatie met overzettreden is vaak al binnen één dag klaar.",
		},
		{
			icon: <ShieldCheck className="w-8 h-8 text-white" />,
			title: "Veilig & Slijtvast",
			description:
				"Onze materialen zijn van hoge kwaliteit, antislip en gaan jarenlang mee.",
		},
		{
			icon: <TrendingUp className="w-8 h-8 text-white" />,
			title: "Waardeverhogend",
			description:
				"Een mooie, veilige trap is een blikvanger en verhoogt de waarde van uw woning.",
		},
	];

	const faqs = [
		{
			question: "Wat kost een traprenovatie?",
			answer:
				"De kosten voor het renoveren van een dichte trap met 12-14 treden beginnen rond de €1.500. De prijs is afhankelijk van het materiaal, de vorm van de trap en extra opties zoals verlichting.",
		},
		{
			question: "Is mijn trap geschikt voor renovatie?",
			answer:
				"Vrijwel elke trap, of deze nu van hout, beton of staal is, kan gerenoveerd worden met overzettreden. Onze specialist kan dit ter plekke beoordelen.",
		},
		{
			question: "Hoeveel overlast geeft een traprenovatie?",
			answer:
				"De overlast is minimaal. Het werk is meestal binnen 1 tot 2 dagen afgerond en u kunt de trap tijdens de renovatie vaak gewoon blijven gebruiken.",
		},
		{
			question: "Welk materiaal kan ik het beste kiezen?",
			answer:
				"Laminaat/PVC is zeer slijtvast en onderhoudsvriendelijk. Echt hout heeft een warmere, authentieke uitstraling. Wij adviseren u graag over de beste keuze voor uw situatie.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1627812961713-6a0576f95374?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxyZW5vdmF0ZWQlMjBzdGFpcmNhc2UlMkMlMjB3b29kZW4lMjBzdGFpcnMlMkMlMjBob21lJTIwaW50ZXJpb3J8ZW58MHx8fHwxNzUxNzQyNDU0fDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1740403016700-28f659e3c7ec?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxtb2Rlcm4lMjBzdGFpcmNhc2UlMkMlMjBzdHlsaXNoJTIwc3RhaXJzJTJDJTIwaG9tZSUyMHJlbm92YXRpb258ZW58MHx8fHwxNzUxNzQyNDU0fDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1740403016700-28f659e3c7ec?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxzdGFpcmNhc2UlMjBkZXNpZ24lMkMlMjBlbGVnYW50JTIwc3RhaXJzJTJDJTIwaG9tZSUyMGltcHJvdmVtZW50fGVufDB8fHx8MTc1MTc0MjQ1NHww&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1714122901841-324b7e0f979d?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxzdGFpciUyMHJlbm92YXRpb24lMkMlMjBiZWF1dGlmdWwlMjBzdGFpcmNhc2UlMkMlMjBpbnRlcmlvciUyMGRlc2lnbnxlbnwwfHx8fDE3NTE3NDI0NTR8MA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1627812961713-6a0576f95374?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxyZW5vdmF0ZWQlMjBzdGFpcmNhc2UlMkMlMjB3b29kZW4lMjBzdGFpcnMlMkMlMjBob21lJTIwaW50ZXJpb3J8ZW58MHx8fHwxNzUxNzQyNDU0fDA&ixlib=rb-4.1.0?w=1024&h=1024"
							alt="Een prachtig gerenoveerde houten trap"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Traprenovatie Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Geef uw trap een complete make-over. Snel, vakkundig en een
								blikvanger in uw interieur.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een trapspecialist{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Traprenovatie Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Voor elke stijl en elk budget een passende oplossing.
							</p>
						</div>
						<div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
							{services.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 100}ms`,
										} as React.CSSProperties
									}
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600 mb-4">{service.description}</p>
									<div className="border-t border-slate-200 pt-4 mt-4">
										<p className="text-slate-700 text-left leading-relaxed">
											{service.longDescription}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Voordelen van Traprenovatie
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Een veilige, mooie en waardevaste investering in uw huis.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={
										{
											"--motion-delay": `${index * 150}ms`,
										} as React.CSSProperties
									}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-indigo-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				<section className="bg-gradient-to-r from-indigo-500 to-purple-600">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Is uw trap aan een nieuwe look toe?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Vraag een vrijblijvende offerte aan en ontdek de mogelijkheden om
							uw trap te transformeren.
						</p>
						<button
							onClick={() =>
								window.open("https://klusgebied.nl/plaats-een-klus", "_blank")
							}
							className="bg-white text-indigo-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Vraag nu een offerte aan
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default Service_TraprenovatiePage;
